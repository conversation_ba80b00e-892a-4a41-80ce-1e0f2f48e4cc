const {
    REQUEST_TYPE,
    PARTNER_CODE,
    CONTRACT_TYPE,
    RESPONSE_CODE,
    roleCode,
    CHANNEL,
    MisaStep,
    ERROR_CODE,
    DOC_TYPE,
    BZHMStep,
    TASK_FLOW,
    WORKFLOW_CODE,
    TIME_CONVERT,
    LOCK_STATUS,
    CIC_STEP_CHECK,
    PRODUCT_CODE,
} = require("../const/definition")
const resConst = require("../const/response-const")
const loanContractRepo = require("../repositories/loan-contract-repo")
const common = require("../utils/common")
const productService = require("../utils/productService")
const { convertBody, convertBodyVer2 } = require("../utils/converter/convert")
const offerRepo = require("../repositories/offer")
const { STATUS, CALLBACK_STAUS } = require("../const/caseStatus")
const utils = require("../utils/helper")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const inputValidator = require("../a1_application/a1-input-validator")
const loggingService = require("../utils/loggingService")
const { baseCheckEligible, baseCheckEligibleSME, checkEligibleMc } = require("../services/de-service")
const { baseCheckS37, checkPcbSME } = require("../services/de-service")
const turnoverRepo = require("../repositories/turnover-repo")
const aadService = require("../utils/aadService")
const callbackService = require("../services/callback-service")
const { ResponseBaseKov } = require("../entity/response-base-kov")
const { RESPONSE_MSG } = require("../const/response-const")
const smsService = require("../utils/smsService")
const { SCHEME } = require("../const/scheme-kov-const")
const { calculatorMaxLoan, makePayloadKovMaxLoanCalculation } = require("../services/kov-de-service")
const { getBusinessDuration } = require("../services/kov-cash-service")
const _ = require('underscore')
const { BadRequestResponse, Response, SuccessResponse, ServerErrorResponse } = require("../base/response")
const Joi = require("joi")
const sqlHelper = require("../utils/sqlHelper")
const loanCustomerRepo = require("../repositories/loan-customer-repo")
const antiFraudService = require("../services/anti-fraud")
const smeMisaService = require("../services/sme-misa-v2")
const masterdataService = require("../utils/masterdataService")
const { LMS_DATE } = require("../utils/dateHelper")
const loggingRepo = require("../repositories/logging-repo")
const {
  ResponseBaseFinv,
  CustomError,
  FinVietSuccessResponse,
  FinVietBadRequestResponse,
  FinVietServerErrorResponse,
  handleResponse,
} = require("../entity/response-base-finv");
const { HTTP_STATUS } = require("../const/variables-const")
const { checkNfcCache } = require("../services/crm-service");
const loanContractDocumentRepo = require("../repositories/document");
const nfcDataRepo = require("../repositories/nfc-data-repo");
const uuid = require("uuid");
const { CHECK_NFC_STEP } = require("../const/step-const")
const { parseDG13 } = require("../utils/nfc")
const loansAf1Repo = require("../repositories/loans-af1-repo");
const { goNextStep } = require("../services/workflow-continue")
const lodash = require("lodash");
const { routing } = require("../services/workflow-service");
const { getSelfieImgFromDG2 } = require("../utils/helper");
class baseA1 {
    constructor(req, res) {
        this.body = req.body
        this.req = req
        this.res = res
        this.config = req.config
    }

    async createLoanRequest() {
        await this.genContractNumber()
        await this.convertBasicBody()
        await this.insertLoan()
        await this.checkEligible()
    }

    async convertBasicBody() {
        const newBody = convertBody(this.body, REQUEST_TYPE.BASIC, global.convertCache)
        this.convertedBody = newBody
        this.convertedBody.contract_number = this.contractNumber
    }

    async checkEligible(isSME = false) {
        try {
            const poolWrite = global.poolWrite
            let responseBody;
            const contractNumber = this.convertedBody.contract_number || this.convertedBody.contractNumber;
            let finalStatus = STATUS.NOT_ELIGIBLE;
            let code = RESPONSE_CODE.INVALID_REQUEST;
            const isSma = this.body.channel === CHANNEL.SMA ? true : false
            if (isSME) {
                // var isEligible = true
                const isEligible = await baseCheckEligibleSME(this.convertedBody);
                if (isEligible) {
                    const isNotBadDebt = await baseCheckS37(this.convertedBody, true);
                    // console.log('isNotBadDebt',isNotBadDebt)
                    if (isNotBadDebt) {
                        const isPassPcb = await checkPcbSME(this.convertedBody, undefined);
                        if (isPassPcb) {
                            finalStatus = STATUS.ELIGIBLE;
                            code = RESPONSE_CODE.RECIEVED;
                        }
                    }
                }

                if (finalStatus != STATUS.ELIGIBLE) {
                    responseBody = {
                        code,
                        message: "NOT ELIGIBLE"
                    }
                }
                else {
                    responseBody = {
                        code,
                        message: "ELIGIBLE",
                        contractNumber
                    }
                }
            } else {
                const isEligible = await baseCheckEligible(this.convertedBody);
                if (isEligible) {
                    const isNotBadDebt = await baseCheckS37(this.convertedBody);
                    // console.log('isNotBadDebt',isNotBadDebt)
                    if (isNotBadDebt) {
                        finalStatus = STATUS.ELIGIBLE;
                        code = RESPONSE_CODE.RECIEVED;
                    }
                }

                if (finalStatus != STATUS.ELIGIBLE) {
                    responseBody = {
                        code,
                        message: "NOT ELIGIBLE"
                    }
                }
                else {
                    responseBody = {
                        code,
                        message: "ELIGIBLE",
                        contractNumber
                    }
                }
            }
            await Promise.all([
                loggingService.saveRequestV2(poolWrite, this.convertedBody, responseBody, this.convertedBody.contract_number, this.convertedBody.request_id, this.convertedBody.partner_code),
                utils.saveStatus(poolWrite, contractNumber, finalStatus),
                offerRepo.createLoanMainScore(contractNumber)
            ])
            if (isSma) {
                let callbackStatus = responseBody.message == "NOT ELIGIBLE" ? CALLBACK_STAUS.NOT_ELIGIBLE : CALLBACK_STAUS.ELIGIBLE
                callbackService.callbackPartner(this.convertedBody.contract_number, this.convertedBody.partner_code, callbackStatus, undefined, undefined, null)
            }
            return responseBody;
        }
        catch (err) {
            common.log('Check eligible a1 error:' + err.message);
        }
    }

    async genContractNumber() {
        const contractNumber = await inputValidator.generateContractNumber_v2(this.req)
        this.contractNumber = contractNumber
    }

    async insertLoan() {
        const insertLoanRs = await loanContractRepo.insertLoanContract(this.convertedBody)
        let responseBody;
        if (!insertLoanRs) {
            responseBody = {
                code: RESPONSE_CODE.SERVER_ERROR,
                message: "Internal Server Error"
            }
            await loggingService.saveRequestV2(this.req.poolWrite, this.convertedBody, responseBody, this.convertedBody.contract_number, this.convertedBody.request_id, this.convertedBody.partner_code)
            throw new Error('insert Loan error');
        }
    }

    async checkCanSubmitCase(body, isSme = false) {
        const poolWrite = global.poolWrite;
        let rowCount = 0;
        if (isSme) {
            let sql = `select * from loan_contract lc where status not in ('ACTIVATED','CANCELLED','REFUSED','NOT_ELIGIBLE','TERMINATED') and sme_name = $1 and sme_tax_id = $2 and sme_phone_number = $3 and contract_type = $4`;
            let params = [body.smeName, body.smeTaxId, body.smePhoneNumber, body.contractType];
            if (!utils.isNullOrEmpty(body.registrationNumber)) {
                sql = `${sql} and registration_number = $5`;
                params.push(body.registrationNumber);
            }
            rowCount = (await poolWrite.query(sql, params)).rowCount || 0;
        } else {
            let sql = `select * from loan_contract lc where status not in ('ACTIVATED','CANCELLED','REFUSED','NOT_ELIGIBLE','TERMINATED') and cust_full_name = $1 and id_number = $2 and phone_number1 = $3 and contract_type = $4`;
            let params = [body.customerName, body.identityCardId, body.phoneNumber, body.contractType];
            rowCount = (await poolWrite.query(sql, params)).rowCount || 0;
        }
        return rowCount;
    }

    responseCreateRequestError() {
        return common.responseErrorPublic(this.res)
    }

    responseInvalidRequest() {
        return this.res.status(200).json({
            code: RESPONSE_CODE.INVALID_REQUEST,
            message: "The customer having contract in process"
        })
    }

}

class kovA1 extends baseA1 {
    constructor(req, res) {
        const currentTimestamp = new Date().getTime()
        const requestId = "KOV" + currentTimestamp
        req.body.partnerCode = PARTNER_CODE.KOV
        req.body.requestId = requestId
        req.body.contractType = CONTRACT_TYPE.CASH_LOAN
        req.body.lms_type = CONTRACT_TYPE.CASH_LOAN
        super(req, res)
    }

    async createLoan() {
        try {
            let validatedResult = [];
            let response;
            validatedResult = await inputValidator.validateInput(this.body, this.req.config, true)
            if (validatedResult.length != 0) {
                response = new ResponseBaseKov(400, RESPONSE_CODE.INVALID_REQUEST, "Request invalid", null, validatedResult);
                return this.res.status(400).json(response);
            }
            const firstConditionIsValid = kovValidateFirst({ timeDuration: this.body.timeDuration, turnover: this.body.turnover });
            if (!firstConditionIsValid) {
                response = new ResponseBaseKov(400, RESPONSE_CODE.INVALID_REQUEST, "Request invalid", null, validatedResult);
                return this.res.status(400).json(response);
            }
            const poolWrite = this.req.poolWrite
            await super.genContractNumber()
            await super.convertBasicBody()
            const contractNumber = this.convertedBody.contract_number
            await super.insertLoan()
            const a1Result = await super.checkEligible();
            let bodyDataResponse = null;
            let responseCode = RESPONSE_CODE.NOT_ELIGIBLE;
            const body = this.req.body;
            let intRateByScheme;
            /*trường hợp eligible*/
            if (a1Result?.code == RESPONSE_CODE.RECIEVED) {
                const schemeAndMaxAmount = kovGetScheme({ turnovers: body.turnover, timeDuration: body.timeDuration, firstRegistrationDate: body.firstRegistrationDate, businessDuration: body.businessDuration });
                // const productData = await productService.getProductInfoV2(schemeAndMaxAmount.scheme)||null
                const promiseData1 = await Promise.all([
                    productService.getProductInfoV2(schemeAndMaxAmount.scheme),
                    turnoverRepo.saveTurnOrTrans(poolWrite, body.turnover, "turnover", contractNumber)
                ]);
                const productData = promiseData1[0];
                // const maxLoanByScheme = parseFloat(productData.maxAmount);
                const maxTenorByScheme = productData.productVar[0].tenorTo;
                intRateByScheme = parseFloat(productData.productVar[0].intRate) / 100;
                const getMaxLoan = await kovGetMaxLoan({ contractNumber, tenor: maxTenorByScheme, interestRate: intRateByScheme, maxLoanByScheme: schemeAndMaxAmount.maxAmount, scheme: schemeAndMaxAmount.scheme });
                bodyDataResponse = {
                    contractNumber: contractNumber,
                    maxLoanAmount: getMaxLoan,
                    maxTenor: maxTenorByScheme,
                    productCode: schemeAndMaxAmount.scheme,
                    intRate: intRateByScheme
                };
                responseCode = RESPONSE_CODE.ELIGIBLE;
            }
            const responseMessage = responseCode;
            response = new ResponseBaseKov(200, responseCode, responseMessage, bodyDataResponse, []);
            Promise.all([
                turnoverRepo.saveTurnOrTrans(poolWrite, body.transaction, "transaction", contractNumber),
                offerRepo.createKOVLoanScore(contractNumber)
            ])
            if (intRateByScheme) loanContractRepo.updateFieldLoanContract(contractNumber, 'request_int_rate', intRateByScheme)
            return this.res.status(200).json(response);
        }
        catch (err) {
            console.log(err);
            return this.res.status(500).json(new ResponseBaseKov(500, RESPONSE_CODE.SERVER_ERROR, RESPONSE_MSG.INTERNAL_SERVER_ERROR, null, []));
        }
    }
}

/*Rule check ban đầu của KOV*/
function kovValidateFirst({ timeDuration, turnover }) {
    if (timeDuration < 3) return false;
    const monthsChecked = [1, 2, 3]
    for (const t of turnover) {
        if (monthsChecked.indexOf(t.month) !== -1) {
            if (t.amount < 10000000) return false;
        }
    }

    return true;
}

function kovGetScheme({ turnovers, timeDuration, firstRegistrationDate, businessDuration }) {
    const MONTHS_CHECKED = [1, 2, 3];
    let turnoverMonthsChecked = [];
    for (const t of turnovers) {
        if (MONTHS_CHECKED.indexOf(t.month) !== -1) {
            turnoverMonthsChecked.push(t);
        }
    }

    //check dieu kien doanh thu
    const rankRevenue = checkRevenue({ turnoverMonthsChecked: turnoverMonthsChecked });
    const rankTimeDuration = checkTimeDuration({ timeDuration });
    const rankRevenueAndTimeDuration = getRankFromRevenueAndTimeDuration({ rankRevenue, rankTimeDuration });
    const rankAndMaxAmountFinal = getRankFromRevenueAndTimeDurationAndRegistration({ rankRevenueAndTimeDuration, firstRegistrationDate, businessDuration });
    return rankAndMaxAmountFinal;

}

function getRankFromRevenueAndTimeDurationAndRegistration({ rankRevenueAndTimeDuration, firstRegistrationDate, businessDuration }) {
    if (rankRevenueAndTimeDuration === SCHEME.KOV_STANDARD) return { scheme: SCHEME.KOV_STANDARD, maxAmount: 200000000, intRate: 0.29 };
    if (rankRevenueAndTimeDuration === SCHEME.KOV_VIP) return { scheme: SCHEME.KOV_VIP, maxAmount: 300000000, intRate: 0.23 };
    if (rankRevenueAndTimeDuration === SCHEME.KOV_PREMIUM) {
        if (firstRegistrationDate) { // co giay dang ky kinh doanh
            return { scheme: SCHEME.KOV_PREMIUM, maxAmount: 500000000, intRate: 0.19 };
        } else {
            return { scheme: SCHEME.KOV_PREMIUM, maxAmount: 300000000, intRate: 0.19 };
        }
    };

    //goi platinum
    if (firstRegistrationDate) businessDuration = getBusinessDuration({ firstRegistrationDate });
    if (businessDuration >= 24) {
        return { scheme: SCHEME.KOV_PLATINUM, maxAmount: 500000000, intRate: 0.16 };
    }
    return { scheme: SCHEME.KOV_PREMIUM, maxAmount: 500000000, intRate: 0.19 };
}

function getRankFromRevenueAndTimeDuration({ rankRevenue, rankTimeDuration }) {
    if (rankRevenue === SCHEME.KOV_STANDARD) return SCHEME.KOV_STANDARD;
    if (rankRevenue === SCHEME.KOV_VIP && [SCHEME.KOV_VIP, SCHEME.KOV_PREMIUM, SCHEME.KOV_PLATINUM].indexOf(rankTimeDuration) !== -1) {
        return SCHEME.KOV_VIP;
    } else if (rankRevenue === SCHEME.KOV_VIP) {
        return SCHEME.KOV_STANDARD;
    }
    if (rankRevenue === SCHEME.KOV_PREMIUM && [SCHEME.KOV_PREMIUM, SCHEME.KOV_PLATINUM].indexOf(rankTimeDuration) !== -1) {
        return SCHEME.KOV_PREMIUM;
    } else if (rankRevenue === SCHEME.KOV_PREMIUM) {
        return SCHEME.KOV_VIP;
    }
    if (rankRevenue === SCHEME.KOV_PLATINUM && rankTimeDuration === SCHEME.KOV_PLATINUM) {
        return SCHEME.KOV_PLATINUM;
    }
    return SCHEME.KOV_PREMIUM;
}

function checkTimeDuration({ timeDuration }) {
    //timeDuration: ex: 24 months
    if (timeDuration >= 24) return SCHEME.KOV_PLATINUM;
    if (timeDuration >= 12) return SCHEME.KOV_PREMIUM;
    if (timeDuration >= 6) return SCHEME.KOV_VIP;
    return SCHEME.KOV_STANDARD;
}

/* Rank: Bình quân doanh thu + ĐK doanh thu mỗi tháng -> RANK*/
const checkRevenue = ({ turnoverMonthsChecked }) => {
    const MONTHS_CHECKED = [1, 2, 3];
    let revenueMonthsChecked = 0;
    let rankMoiThang = SCHEME.KOV_PLATINUM;
    for (const t of turnoverMonthsChecked) {
        if (MONTHS_CHECKED.indexOf(t.month) !== -1) {
            revenueMonthsChecked = revenueMonthsChecked + t.amount;
            if (10000000 <= t.amount && t.amount < 20000000 && [SCHEME.KOV_PLATINUM, SCHEME.KOV_PREMIUM, SCHEME.KOV_VIP].includes(rankMoiThang)) {
                rankMoiThang = SCHEME.KOV_STANDARD;
            } else if (20000000 <= t.amount && t.amount < 50000000 && [SCHEME.KOV_PLATINUM, SCHEME.KOV_PREMIUM].includes(rankMoiThang) && rankMoiThang !== SCHEME.KOV_STANDARD) {
                rankMoiThang = SCHEME.KOV_VIP;
            } else if (50000000 <= t.amount && t.amount < 300000000 && rankMoiThang === SCHEME.KOV_PLATINUM && ![SCHEME.KOV_VIP, SCHEME.KOV_STANDARD].includes(rankMoiThang)) {
                rankMoiThang = SCHEME.KOV_PREMIUM;
            } else if (t.amount >= 300000000 && ![SCHEME.KOV_PREMIUM, SCHEME.KOV_VIP, SCHEME.KOV_STANDARD].includes(rankMoiThang)) {
                let revenueEveryMonthPLA = true;
                rankMoiThang = SCHEME.KOV_PLATINUM;
            }
        }
    }

    const avgRevenueMonthsChecked = revenueMonthsChecked / MONTHS_CHECKED.length;
    let rankBQDT; //rank binh quan doanh thu
    if (10000000 <= avgRevenueMonthsChecked && avgRevenueMonthsChecked < 100000000) {
        rankBQDT = SCHEME.KOV_STANDARD
    } else if (100000000 <= avgRevenueMonthsChecked && avgRevenueMonthsChecked < 300000000) {
        rankBQDT = SCHEME.KOV_VIP
    } else if (300000000 <= avgRevenueMonthsChecked && avgRevenueMonthsChecked < 1000000000) {
        rankBQDT = SCHEME.KOV_PREMIUM
    } else if (1000000000 <= avgRevenueMonthsChecked) {
        rankBQDT = SCHEME.KOV_PLATINUM
    }

    if (rankBQDT === SCHEME.KOV_PLATINUM) {
        return rankMoiThang;
    } else if (rankBQDT === SCHEME.KOV_PREMIUM) {
        if (rankMoiThang && rankMoiThang === SCHEME.KOV_PLATINUM) { //thỏa điều kiện về bình quân doanh thu, nhưng không thỏa DT mỗi tháng 
            return SCHEME.KOV_PREMIUM;                              // hạ từ Platinum -> Premium
        }
        return rankMoiThang;
    } else if (rankBQDT === SCHEME.KOV_VIP) {
        if (rankMoiThang && [SCHEME.KOV_PLATINUM, SCHEME.KOV_PREMIUM, SCHEME.KOV_VIP].indexOf(rankMoiThang) !== -1) {
            return SCHEME.KOV_VIP;
        }
        return rankMoiThang;
    }

    return rankBQDT; //standard
}

const kovGetMaxLoan = async ({ contractNumber, tenor, interestRate, maxLoanByScheme, scheme }) => {

    const loanContractEntity = await loanContractRepo.getLoanContract(contractNumber);
    let requestBody = await makePayloadKovMaxLoanCalculation({ loanContractEntity, interestRate, tenor });
    requestBody.scheme = scheme;

    const getMaxLoan = await calculatorMaxLoan(requestBody);
    const { offer } = getMaxLoan;
    //min(maxloan1,maxloan2, 500,000,000, số tiền tối đa theo scheme)
    // return kovGetMaxLoanAf1({ maxLoan1, maxLoan2, maxLoanByScheme });
    return offer;
}

function kovGetMaxLoanAf1({ maxLoan1, maxLoan2, maxLoanByScheme }) {
    let min = maxLoan1;
    if (min > maxLoan2) min = maxLoan2;
    if (min > maxLoanByScheme) min = maxLoanByScheme;
    if (min > 500000000) min = 500000000;
    return min;
}

class MisaA1 extends baseA1 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.MISA);
        req.body.partnerCode = PARTNER_CODE.MISA;
        req.body.channel = 'MC';
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
        if (req.body.contractType === CONTRACT_TYPE.CREDIT_LINE) {
            req.body.productCode = 'SME_MISA_HM_STANDARD';
        }
        else {
            req.body.productCode = 'SME_MISA_VM_STANDARD';
        }
        if (req.body.isAuthorizationSign === 'N') req.body.authorizedGender = null;
        req.body.requestId = requestId;
        super(req, res);
        this.partnerCode = PARTNER_CODE.MISA
    }
    #validateSmeInfo(smeInfo) {
        let result = {
            code: 'ok'
        }
        const timeDurationSmeNet = 12;
        const timeDurationMeInvoice = 6;
        const notPassCode = 'not ok';
        const socalInsuranceStaffNumberRule = parseInt(200);
        const totalCapitalLastYearRule = parseInt(100000000000);
        const totalTurnoverNextYearRule = parseInt(300000000000);
        if (parseInt(smeInfo.socalInsuranceStaffNumber) > socalInsuranceStaffNumberRule) {
            result.code = notPassCode;
            result.location = 'socalInsuranceStaffNumber';
            result.message = 'Số lao động tham gia bảo hiểm xã hội bình quân năm không quá 200 người.';
        }
        if (parseInt(smeInfo.totalCapitalLastYear) > totalCapitalLastYearRule && parseInt(smeInfo.totalTurnoverNextYear) > totalTurnoverNextYearRule) {
            result.code = notPassCode;
            result.location = 'totalCapitalLastYear & totalTurnoverNextYear';
            result.message = 'Tổng nguồn vốn không quá 100 tỷ đồng và Tổng doanh thu của năm trước liền kề không quá 300 tỷ đồng.';
        }
        if (smeInfo.nameOfApp == 'SME.NET') {
            if (parseFloat(smeInfo.profitAfterTax) < 0.0) {
                result.code = notPassCode;
                result.location = 'profitAfterTax';
                result.message = 'Lợi nhuận sau thuế TNDN (NPAT) BCKQKD trong năm gần nhất trên MISA SME.NET > 0.0tr VND.';
            }
            if (parseInt(smeInfo.timeDuration) < timeDurationSmeNet) {
                result.code = notPassCode;
                result.location = 'timeDuration';
                result.message = 'Thời gian sử dụng MISA SME.NET ≥ 12 tháng.';
            }
        }
        if (smeInfo.nameOfApp == 'Meinvoice') {
            if (parseInt(smeInfo.timeDuration) < timeDurationMeInvoice) {
                result.code = notPassCode;
                result.location = 'timeDuration';
                result.message = 'Thời gian sử dụng Meinvoice ≥ 6 tháng.';
            }
        }
        return result;
    }

    async createLoan() {
        try {
            const canSubmit = await super.checkCanSubmitCase(this.body, true);
            if (canSubmit > 0) {
                super.responseInvalidRequest();
            } else {
                const smsUrl = this.config.data.smsService.sendSMS;
                const isEnable = this.config.data.smsService.useSMS;
                const msg = this.config.data.smsService.notEligibleMsg;
                let phoneNumberRs;
                const body = this.body;
                const validateSmeInfoResult = this.#validateSmeInfo(body);
                if (validateSmeInfoResult?.code != 'ok') {
                    phoneNumberRs = body.smeRepresentationPhoneNumber || undefined;
                    try {
                        if (phoneNumberRs !== undefined) {
                            if (isEnable) {
                                await smsService.sendSMS(this.contractNumber, msg, smsUrl, phoneNumberRs, true)
                            }
                        }
                    }
                    catch (error) {
                        console.log(error)
                    }
                    return this.res.status(200).json({
                        code: RESPONSE_CODE.INVALID_REQUEST,
                        message: 'Request is invalid',
                        errors: {
                            code: 'INVALID',
                            location: validateSmeInfoResult?.location,
                            message: validateSmeInfoResult?.message
                        }
                    })
                }
                if (!utils.isNullOrEmpty(body.profit)) {
                    let totalProfit = 0;
                    for await (const item of body.profit) {
                        totalProfit += item.amount;
                    }
                    body.totalProfit = totalProfit;
                }
                await super.genContractNumber()
                await super.convertBasicBody()
                await super.insertLoan()
                const a1Result = await super.checkEligible(true)
                if (!utils.isNullOrEmpty(body.turnover)) {
                    turnoverRepo.saveTurnOrTrans(this.req.poolWrite, body.turnover, "turnover", this.contractNumber);
                }
                if (a1Result?.code == RESPONSE_CODE.RECIEVED) {
                    await aadService.pushTaskMcV2(roleCode.SS, this.contractNumber, body.contractType, STATUS.IN_SS_MAXLOAN_QUEUE);
                    await loanContractRepo.updateContractStatus(STATUS.IN_SS_MAXLOAN_QUEUE, this.contractNumber);
                }
                if ([STATUS.NOT_ELIGIBLE2, STATUS.NOT_ELIGIBLE].includes(a1Result.message)) {
                    phoneNumberRs = await utils.getPhoneNumberSme(this.contractNumber)
                    try {
                        if (phoneNumberRs !== undefined) {
                            if (isEnable) {
                                await smsService.sendSMS(this.contractNumber, msg, smsUrl, phoneNumberRs, true)
                            }
                        }
                    }
                    catch (error) {
                        console.log(error)
                    }
                }
                return this.res.status(200).json(a1Result);
            }
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

class McAppA1 extends baseA1 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.MCAPP);
        req.body.partnerCode = PARTNER_CODE.MCAPP;
        req.body.requestId = requestId;
        req.body.contractType = CONTRACT_TYPE.CREDIT_LINE;
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE;
        req.body.channel = 'MC';
        super(req, res);
    }

    async createLoan() {
        try {
            await super.genContractNumber();
            await super.convertBasicBody();
            await super.insertLoan();
            const a1Result = await super.checkEligible(false);
            return a1Result;
            // return this.res.status(200).json(a1Result);
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

// class SuperAppA1 extends baseA1 {
//     constructor(req, res) {
//         // const requestId = utils.genRequestId(PARTNER_CODE.SMA);
//         // req.body.partnerCode = PARTNER_CODE.SMA;
//         // req.body.requestId = requestId;
//         // req.body.contractType = CONTRACT_TYPE.CREDIT_LINE;
//         // req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE;
//         super(req, res);
//     }

//     async createLoan() {
//         try {
//             const convertedBody = convertBody(this.body,REQUEST_TYPE.BASIC,this.req.convertCache)
//             this.convertedBody = convertedBody
//             const productRate = await productService.getRateByRequestAmount(this.convertedBody?.request_amt);
//             if (!utils.isNullOrEmpty(productRate)) {
//                 if (parseInt(productRate) != 0)
//                     loanContractRepo.updateFieldLoanContract(this.body.contractNumber, 'request_int_rate', parseInt(productRate) / 100)
//             }
//             loanContractRepo.updateLoanContract(this.convertedBody)
//             // const contractNumber = this.body.contractNumber || this.body.contract_number
//             // const loanData = await loanContractRepo.getLoanContract(contractNumber)

//             // await super.genContractNumber();
//             // await super.convertBasicBody();
//             // await super.insertLoan();
//             const a1Result = await super.checkEligible(false, true);
//             return a1Result;
//         }
//         catch(err) {
//             console.log(err)
//             return false;
//         }
//     }
// }

class SuperAppA1 extends baseA1 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.SMA);
        req.body.partnerCode = PARTNER_CODE.SMA;
        req.body.requestId = requestId;
        req.body.contractType = CONTRACT_TYPE.CREDIT_LINE;
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE;
        req.body.channel = CHANNEL.SMA;
        super(req, res);
    }

    async createLoan() {
        try {
            await super.genContractNumber();
            await super.convertBasicBody();
            await super.insertLoan();
            const a1Result = await super.checkEligible(false);
            return a1Result;
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

class MisaAf1 extends baseA1 {
    constructor(req, res) {
        req.body.partnerCode = PARTNER_CODE.MISA;
        req.body.channel = 'MC';
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
        super(req, res);
        this.partnerCode = PARTNER_CODE.MISA
        req.body.status = STATUS.RECEIVEDA1
    }
    validateAf1(requestPayload) {
        const schema = Joi.object({
            requestId: Joi.string().required(),
            taxId: Joi.string().required(),
            companyName: Joi.string().required(),
            addressOnLicense: Joi.string().required(),
            addressOnLicenseDetail: Joi.object({
                provinceCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                districtCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                wardCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                detailAddress: Joi.string().required(),
                newProvinceCode: Joi.string().optional().allow(null, ''),
                newWardCode: Joi.string().optional().allow(null, ''),
            }).unknown(true).required(),
            representations: Joi.array().items(
                Joi.object({
                    fullName: Joi.string().required(),
                    position: Joi.string().required(),
                    dob: Joi.string().required(),
                    id: Joi.string().required(),
                    issueDate: Joi.string().required(),
                    issuePlace: Joi.string().required(),
                    phoneNumber: Joi.string().required()
                }).unknown(true).required()
            ).required(),
            isChange: Joi.number().valid(0,1).required(),
            changedInfo: Joi.object({
                businessLicenseUrl: Joi.string().uri().optional(),
                companyName: Joi.string().optional(),
                provinceCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                districtCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                wardCode: Joi.when('newWardCode', {
                    is: Joi.valid(null, ''),
                    then: Joi.string().required(),
                    otherwise: Joi.optional()
                }),
                detailAddress: Joi.string().required(),
                newProvinceCode: Joi.string().optional().allow(null, ''),
                newWardCode: Joi.string().optional().allow(null, ''),
            }).unknown(true).optional()
        }).unknown(true)
        const { error } = schema.validate(requestPayload)
        if (error) {
            console.log(`error: `, JSON.stringify(error))
            return { isValid: false, errorCode: error?.details?.[0]?.context?.key, errorMessage: error?.details?.[0].message }
        }
        return { isValid: true }
    }

    async getProcessingCase(body) {
        const poolWrite = global.poolWrite;
        let rowCount = 0;
        let sql = `
            select 
                * 
            from 
                loan_contract lc 
            where 
                sme_name = $1 and sme_tax_id = $2
                and status not in ($3, $4, $5, $6, $7, $8, $9)
            `;
        let params = [body.companyName, body.taxId, STATUS.ACTIVATED, STATUS.CANCELLED, STATUS.REFUSED, STATUS.NOT_ELIGIBLE, STATUS.TERMINATED, STATUS.BAD_DEBT, STATUS.INACTIVATED];
        rowCount = (await poolWrite.query(sql, params)).rowCount || 0;
        return rowCount;
    }

    async convertAf1Body() {
        const newBody = convertBody(this.body, REQUEST_TYPE.MISA_AF1, global.convertCache)
        this.convertedBody = newBody
        this.convertedBody.contract_number = this.contractNumber
    }

    async processAf1() {
        try {
            const processingCase = await this.getProcessingCase(this.body);
            if (processingCase > 0) {
                const errors = [{
                    errorCode: `status`,
                    errorMessage: `Quý khách đang có hồ sơ chưa hoàn thành, không thể submit`
                }]
                return this.res.status(400).json(new BadRequestResponse(errors));
            }

            const misaBody = this.body;
            const validateSmeInfoResult = this.validateAf1(misaBody);
            if (!validateSmeInfoResult.isValid) {
                const errors = [{
                    errorCode: validateSmeInfoResult.errorCode,
                    errorMessage: validateSmeInfoResult.errorMessage
                }]
                return this.res.status(400).json(new BadRequestResponse(errors));
            }
            const body = await masterdataService.convertEvfLov({ partnerCode: PARTNER_CODE.MISA, convertObject: misaBody });
            await super.genContractNumber();
            body.contractNumber = this.contractNumber
            await this.convertAf1Body();
            let loanCustomerData = { ...body };
            if (body.isChange == 1) {
                loanCustomerData = {
                    ...loanCustomerData,
                    companyName: body?.changedInfo?.companyName,
                    addressOnLicense: body?.changedInfo?.addressOnLicense,
                    provinceOnLicense: body?.changedInfo?.provinceCode,
                    districtOnLicense: body?.changedInfo?.districtCode,
                    wardOnLicense: body?.changedInfo?.wardCode,
                    detailOnLicense: body?.changedInfo?.detailAddress,
                    newProvinceOnLicense: body?.changedInfo?.newProvinceCode,
                    newWardOnLicense: body?.changedInfo?.newWardCode,
                    businessLicenseUrl: body?.changedInfo?.businessLicenseUrl,
                    // new address
                    newProvinceCode: body?.changedInfo?.newProvinceCode,
                    newWardCode: body?.changedInfo?.newWardCode,
                    oldInfo: JSON.stringify({
                        companyName: body?.companyName,
                        addressOnLicense: body?.addressOnLicense,
                        provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                        districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                        wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                        detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                        businessLicenseUrl: body?.businessLicenseUrl,
                        // new address
                        newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                        newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                    })
                }
            } else {
                loanCustomerData = {
                    ...loanCustomerData,
                    addressOnLicense: body?.addressOnLicense,
                    provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                    districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                    wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                    detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                    // new address
                    newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                    newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                }
            }
            await super.insertLoan();
            await Promise.all([
                loanContractRepo.insertLoanCustomerRepresentations(this.contractNumber, body.registrationNumber, body.representations),
                sqlHelper.insertData(
                    `loan_customer`,
                    loanCustomerRepo.columns,
                    sqlHelper.generateValues(utils.convertCamelToSnake(loanCustomerData), loanCustomerRepo.columns)
                ),
                loggingRepo.saveWorkflow(MisaStep.AF1, STATUS.RECEIVEDA1, this.contractNumber, 'system')
            ])
            //check cic here
            const cicBody = {
                contractNumber: this.contractNumber,
                persons: body.representations.map((rep) => ({
                    idNumber: rep.id,
                    otherIdNumber: rep.otherId,
                    fullName: rep.fullName,
                    address: ''
                })),
                // enterprises: [{
                //     registrationNumber: '',
                //     taxCode: body.taxId,
                //     companyName: body.companyName,
                //     address: body.addressOnLicense,
                // }]
            }
            antiFraudService.checkCicB11t(cicBody).then((respData) =>
                this.handleCicResult(this.contractNumber, respData)
            );
            try {
                await this.saveFiles(body, this.contractNumber);
            } catch (e) {
                console.error(e);
                console.log(`${this.contractNumber} | AF1 | saveFiles error: `, e);
            }
            const respData = {
                contractNumber: this.contractNumber,
                status: STATUS.RECEIVEDA1
            }
            return this.res.status(200).json(new SuccessResponse(respData));
        } catch (e) {
            console.error(e)
            return this.res.status(500).json(new ServerErrorResponse());
        }
    }

    handleCicResult = async (contractNumber, cicResult) => {
        try {
            const {
                decision
            } = cicResult || {};
            if (decision && [STATUS.ELIGIBLE, STATUS.NOT_ELIGIBLE].includes(decision)) {
                await loanContractRepo.updateContractStatus(decision, contractNumber);
                await loggingRepo.saveWorkflow(MisaStep.AF1, decision, contractNumber, 'system');
                //handle callback misa here
                const isPass = decision === STATUS.ELIGIBLE ? true : false;
                await smeMisaService.callbackCicResult({
                    step: smeMisaService.CIC_STEP_MISA_CALLBACK.AF1,
                    contractNumber,
                    isPass,
                    loanEffectTime: cicResult?.nextTimeCanRequest ?? LMS_DATE()
                });
            }
        } catch (e) {
            console.error(e)
        }
    }

    saveFiles = async (body, contractNumber) => {
        const {
            businessLicenseUrl
        } = body
        let documents = [];
        if (businessLicenseUrl) {
            let businessLicenseUrlContentType = utils.getContentTypeFromMisaUrl(businessLicenseUrl);
            let businessLicenseDoc = {
                fileUrl: businessLicenseUrl,
                fileType: businessLicenseUrlContentType,
                docType: 'SBIZ'
            }
            documents.push(businessLicenseDoc);
        }
        if (body?.changedInfo?.businessLicenseUrl) {
            let newLicenseUrl = body.changedInfo.businessLicenseUrl;
            let newLicenseUrlContentType = utils.getContentTypeFromMisaUrl(newLicenseUrl);
            let newLicenseDoc = {
                fileUrl: newLicenseUrl,
                fileType: newLicenseUrlContentType,
                docType: 'SBIZ'
            }
            documents.push(newLicenseDoc);
        }
        if (documents?.length > 0) {
            await smeMisaService.saveAf2Documents(documents, contractNumber, { req: this.req })
        }
    }
}

/**
 * class handle af1 finviet partner
 */
class FinvA1 extends baseA1 {
  constructor(req, res) {
    req.body.partnerCode = PARTNER_CODE.FINV;
    req.body.contractType = CONTRACT_TYPE.CREDIT_LINE;
    req.body.lmsType = CONTRACT_TYPE.CREDIT_LINE;
    super(req, res);
  }

  async ocrAndCheckNfc() {
    const { docs, requestId, customerInfo, nfcData } = this.body;
    const frontIdCard = docs.find((e) => e.docType == DOC_TYPE.ID_CARD.FRONT);
    const backIdCard = docs.find((e) => e.docType == DOC_TYPE.ID_CARD.BACK);
    if (!frontIdCard || !backIdCard) {
      throw Error("missing frontIdCard-PID or backIdCard-PID1");
    }
    const frontIdCardFileKey = utils.getFileKeyFromUrl(frontIdCard.fileUrl);
    const backIdCardFileKey = utils.getFileKeyFromUrl(backIdCard.fileUrl);

    let ocrData = await antiFraudService.ocrIdCard({
      requestId: requestId,
      s3FrontIdCardUrl: frontIdCardFileKey,
      s3BackIdCardUrl: backIdCardFileKey,
    });
    ocrData = utils.snakeToCamel(ocrData);
    const { identityCard } = customerInfo;
    const checkNfcCacheRs = await checkNfcCache({
      idNumber: identityCard,
      issueDate: ocrData.issueDate,
    });
    const task = [];
    // const ocrIssueDate = moment(ocrData.issueDate, "DD/MM/YYYY").format(
    //   "YYYY-MM-DD"
    // );
    let isValid = true;
    // if (
    //   (checkNfcCacheRs.exists &&
    //     checkNfcCacheRs.oldIssueDate != ocrIssueDate) ||
    //   (!checkNfcCacheRs.exists && checkNfcCacheRs.oldIssueDate)
    // ) {
    //   isValid = false;
    // }
    // if (isValid) {
    //   await super.genContractNumber();
    await antiFraudService.checkNfc({});
    task.push(
      loanContractDocumentRepo.insert({
        contractNumber: this.contractNumber,
        docType: DOC_TYPE.ID_CARD.FRONT,
        docId: uuid.v4(),
        fileKey: frontIdCardFileKey,
        fileName: utils.getFileNameFromUrl(frontIdCardFileKey),
        url: frontIdCard.fileUrl,
      })
    );
    task.push(
      loanContractDocumentRepo.insert({
        contractNumber: this.contractNumber,
        docType: DOC_TYPE.ID_CARD.BACK,
        docId: uuid.v4(),
        fileKey: backIdCardFileKey,
        fileName: utils.getFileNameFromUrl(backIdCardFileKey),
        url: backIdCard.fileUrl,
      })
    );
    //}
    task.push(
      nfcDataRepo.insert({
        requestId,
        nfcRawData: JSON.stringify(nfcData || []),
        contractNumber: this.contractNumber || null,
        frontIdCardUrl: frontIdCardFileKey,
        backIdCardUrl: backIdCardFileKey,
        ocrData,
        nfcIssueDate: checkNfcCacheRs.oldIssueDate,
        exists: checkNfcCacheRs.exists,
      })
    );
    await Promise.all(task);
    return { isValid, oldIssueDate: checkNfcCacheRs.oldIssueDate };
  }

    async createLoan() {
        try {
            let body = this.body;
            const { dob, gender, issueDate } = parseDG13(body.nfcData.dg13);
            body.birthDate = dob !== '' ? moment(dob, "DD/MM/YYYY").format("YYYY-MM-DD") : null;
            body.gender = gender === 'Nam' ? 'M' : 'F';
            body.idIssueDt = issueDate !== '' ? moment(issueDate, "DD/MM/YYYY").format("YYYY-MM-DD") : null;
            body.phoneNumber = body.customerInfo.phoneNumber;
            body.status = STATUS.RECEIVEDA1;
            await super.genContractNumber();
            this.convertedBody(body);
            const loansAf1Data = {
                request_id: body.requestId,
                company_name: body.companyName,
                customer_name: body.customerInfo.fullname,
                id_number: body.customerInfo.identityCard,
                nfc_value: JSON.stringify(body.nfcData),
                contract_number: this.contractNumber,
                partner_code: body.partnerCode,
                phone_number: body.customerInfo.phoneNumber
            };
            const dg2ImgData = await getSelfieImgFromDG2(body.nfcData.dg2, this.contractNumber);
            await Promise.all([
                this.insertLoan(),
                sqlHelper.insertData(
                    `loans_af1`,
                    loansAf1Repo.columns,
                    sqlHelper.generateValues(utils.convertCamelToSnake(loansAf1Data), loansAf1Repo.columns)
                ),
                sqlHelper.insertData(
                    'loan_contract_document',
                    [
                        'contract_number',
                        'doc_type',
                        'file_name',
                        'url',
                        'doc_id',
                        'file_key'
                    ],
                    [
                        this.contractNumber,
                        'NFC',
                        dg2ImgData.fileName,
                        dg2ImgData.s3Rs.Location,
                        uuid.v4(),
                        dg2ImgData.s3Rs.Key,
                    ]
                )
            ]);
            const responseData = new FinVietSuccessResponse({
                contractNumber: this.contractNumber,
                status: this.convertedBody.status,
            });
            goNextStep(this.contractNumber);
            return handleResponse(this.res, responseData);
        } catch (error) {
            console.log(
                `[FINV][AF1][createLoan] payload ${JSON.stringify(
                    this.body
                )}, error ${error}`
            );
            const response = new FinVietServerErrorResponse();
            return handleResponse(this.res, response);
        }
    }

  async checkNfc() {
    try {
      const checkRs = await antiFraudService.checkNfc(
        this.contractNumber,
        false,
        CHECK_NFC_STEP.AF1,
        false
      );
      parseDG13(this.body.nfcData.dg13);
      if (checkRs?.decision != STATUS.ELIGIBLE) {
        this.convertedBody.status = STATUS.NOT_ELIGIBLE;
        await loanContractRepo.updateContractStatus(
          this.convertedBody.status,
          this.contractNumber
        );
        return false;
      }
      return true;
    } catch (error) {
      this.convertedBody.status = STATUS.CANCELLED;
      console.log(
        `[FINV][AF1][checkNfc] contractNumber: ${this.contractNumber} error ${error}`
      );
      throw error;
    } finally {
      await loanContractRepo.updateContractStatus(
        this.convertedBody.status,
        this.contractNumber
      );
    }
  }

  async ocrData() {
    const { docs, requestId, nfcData } = this.body;
    const frontIdCard = docs.find((e) => e.docType == DOC_TYPE.ID_CARD.FRONT);
    const backIdCard = docs.find((e) => e.docType == DOC_TYPE.ID_CARD.BACK);
    if (!frontIdCard || !backIdCard) {
      throw Error("missing frontIdCard-PID or backIdCard-PID1");
    }
    const frontIdCardFileKey = utils.getFileKeyFromUrl(frontIdCard.fileUrl);
    const backIdCardFileKey = utils.getFileKeyFromUrl(backIdCard.fileUrl);

    let ocrData = await antiFraudService.ocrIdCard({
      requestId: requestId,
      s3FrontIdCardUrl: frontIdCardFileKey,
      s3BackIdCardUrl: backIdCardFileKey,
    });
    ocrData = utils.snakeToCamel(ocrData);
    if (!ocrData) {
      throw Error("OCR data failed");
    }
    await nfcDataRepo.insert({
      requestId,
      nfcRawData: JSON.stringify(nfcData || []),
      contractNumber: this.contractNumber || null,
      frontIdCardUrl: frontIdCardFileKey,
      backIdCardUrl: backIdCardFileKey,
      ocrData,
    });
    this.body.ocrData = ocrData;
  }

  async insertLoan() {
    const { docs } = this.body;
    const task = [];
    const bundleInfo = await productService.getBundleV4(
      global.config,
      this.body.productCode
    );
    const documentList = productService.mapBundleGroupV2(docs, bundleInfo || []);
    for (const doc of documentList) {
      task.push(
        sqlHelper.updateData({
          table: "loan_contract_document",
          columns:[
            'contract_number',
            'doc_type',
            'doc_group',
            'doc_name',
            'doc_name_vn'
          ],
          values: [
            this.contractNumber,
            doc.docType,
            doc.docGroup,
            doc.bundleName,
            doc.bundleNameVi
          ],
          conditions: {
            doc_id: doc.docId,
          }
        })
      );
    }
    await Promise.all(task);
    await super.insertLoan();
  }

  convertedBody(originalBody) {
    originalBody.contractNumber = this.contractNumber;
    originalBody.currentTask = TASK_FLOW.START;
    originalBody.workflowCode = WORKFLOW_CODE.FINV_AF1
    this.convertedBody = convertBodyVer2(
      originalBody,
      REQUEST_TYPE.FINV_AF1,
      global.convertCache
    );
  }

  async handleValidationStep(stepFunction) {
    const responseData = await stepFunction();
    if (!responseData) {
      this.convertedBody.status = STATUS.NOT_ELIGIBLE;
      await loanContractRepo.updateContractStatus(
        this.convertedBody.status,
        this.contractNumber
      );
    }
    return responseData;
  }

  async checkS37() {
    let payload = {};
    try {
      payload = {
        contractNumber: this.contractNumber,
        partnerCode: PARTNER_CODE.FINV,
        persons: [
          {
            idNumber: this.body.customerInfo.identityCard,
            fullName: this.body.customerInfo.fullname,
          },
        ],
        enterprises: [],
      };
      const cicResult = await antiFraudService.checkS37Sme(payload);
      if (!cicResult?.decision) {
        throw Error(`Check S37 ERROR`);
      }
      console.log(
        `[FINV][AF1][checkS37] contractNumber: ${
          this.contractNumber
        } payload ${JSON.stringify(payload)}, response ${JSON.stringify(
          cicResult
        )}`
      );
      this.convertedBody.status = STATUS.ELIGIBLE;
      if (cicResult.decision != STATUS.ELIGIBLE) {
        this.convertedBody.status = STATUS.NOT_ELIGIBLE;
        return false;
      }
      return true;
    } catch (error) {
      console.log(
        `[FINV][AF1][checkS37] contractNumber: ${
          this.contractNumber
        } payload ${JSON.stringify(payload)}, error ${error}`
      );
      this.convertedBody.status = STATUS.CANCELLED;
      throw error;
    } finally {
      await loanContractRepo.updateContractStatus(
        this.convertedBody.status,
        this.contractNumber
      );
    }
  }

  async checkB11t() {
    let payload = {};
    payload = {
      contractNumber: this.contractNumber,
      partnerCode: PARTNER_CODE.FINV,
      persons: [
        {
          idNumber: this.body.customerInfo.identityCard,
          fullName: this.body.customerInfo.fullname,
        },
      ],
    };
    const cicResult = await antiFraudService.checkCicB11t(payload);
    if (!cicResult?.decision) {
      throw Error(`[FINV] [AF1] Check B11T ERROR`);
    }
    console.log(
      `[FINV][AF1][checkB11T] contractNumber: ${
        this.contractNumber
      } payload ${JSON.stringify(payload)}, response ${JSON.stringify(
        cicResult
      )}`
    );
    return cicResult.decision === STATUS.ELIGIBLE;
  }

  async checkRenewDate() {
    const { success, data } = await antiFraudService.checkRenewDateByIdentityCard(
      this.body.requestId,
      this.body.partnerCode,
      this.body.customerInfo.identityCard,
      this.contractNumber,
    )
    if (!success) {
      throw new Error('[FINV][AF1] Check contract renew date error')
    }
    return !data.data.is_locking;

  }

  async checkEligibility() {
    // TODO: Handle logic check contract status
    // TODO: Handle logic check whitelist
    return true;
  }

  async checkBlacklist() {
    const { success, data } = await antiFraudService.checkBlacklist({
      request_id: this.body.requestId,
      partner_code: this.body.partnerCode,
      id_number: this.body.customerInfo.identityCard,
      contract_number: this.contractNumber,
    });
    if (!success) {
      throw new Error('[FINV] [AF1] Check blacklist error')
    }
    if (![STATUS.ELIGIBLE, STATUS.NOT_ELIGIBLE].includes(data.code)) {
      return new FinVietBadRequestResponse('Check blacklist error: decision invalid', {}, []);
    }
    return data.code === STATUS.ELIGIBLE;
  }

  async checkOCR() {
    // TODO: Handle logic check OCR
    return true;
    // await this.ocrData();
  }

  async checkEKYC() {
    // TODO: Handle logic check eKYC
    return true;
  }

  async checkNFCCache() {
    // TODO: Handle logic check NFC Cache
    return true;
    // await this.checkNfc();
  }

  async checkCacheC06() {
    // TODO: Handle logic check cache C06
    return true;
  }

  async evaluateContract () {
    try {
      // Validation steps with early return on error
      let validationResult;

      // Check contract renew date
      validationResult = await this.handleValidationStep(
        () => this.checkRenewDate(),
      );
      if (!validationResult) return validationResult;

      // Check eligibility
      validationResult = await this.handleValidationStep(
        () => this.checkEligibility(this.body)
      );
      if (!validationResult) return validationResult;

      // Check blacklist
      validationResult = await this.handleValidationStep(
        () => this.checkBlacklist()
      );
      if (!validationResult) return validationResult;

      validationResult = await this.handleValidationStep(
        () => this.checkOCR()
      );
      if (!validationResult) return validationResult;

      validationResult = await this.handleValidationStep(
        () => this.checkEKYC()
      );
      if (!validationResult) return validationResult;

      validationResult = await this.handleValidationStep(
        () => this.checkNFCCache()
      );
      if (!validationResult) return validationResult;

      validationResult = await this.handleValidationStep(
        () => this.checkCacheC06()
      );
      if (!validationResult) return validationResult;

      validationResult = await this.handleValidationStep(
        () => this.checkB11t()
      );
      if (!validationResult) return validationResult;

      // After checking, set status to ELIGIBLE
      this.convertedBody.status = STATUS.ELIGIBLE;
      await loanContractRepo.updateContractStatus(
        this.convertedBody.status,
        this.contractNumber
      );
    } catch (err) {
      console.log('[FINV] [AF1] Error occurred when evaluating contract:', err);
    }
  }
}

class MisaTc1 extends MisaAf1 {
    constructor(req, res) {
        // baseA1
        super(req, res);

        // for MISA TC1
        req.body.channel = 'MC';
        req.body.contractType = CONTRACT_TYPE.CREDIT_LINE
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
        this.partnerCode = PARTNER_CODE.MISA
        req.body.status = STATUS.RECEIVEDTC1
        this.poolWrite = req.poolWrite
    }

    async convertTc1Body(body) {
        const newBody = convertBody(body, REQUEST_TYPE.MISA_TC1, global.convertCache)
        this.convertedBody = newBody
    }

    validateTc1(requestPayload) {
        const schema = Joi.object({
            requestId: Joi.string().required(),
            oldContractNumber: Joi.string().required(), // for MISA TC1
            productCode: Joi.string().required(), // for MISA TC1
            taxId: Joi.string().required(),
            registrationNumber: Joi.string().required(),
            companyName: Joi.string().required(),
            addressOnLicense: Joi.string().required(), // Địa chỉ trên GPKD MISA
            addressOnLicenseDetail: Joi.object({ // Địa chỉ chi tiết trên GPKD MISA
                provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                detailAddress: Joi.string().required(),
                newProvinceCode: Joi.string().allow(null).allow(''),
                newWardCode: Joi.string().allow(null).allow('')
            }).required(),
            isChange: Joi.number().valid(0,1).required(),
            changedInfo: Joi.when('isChange', { is: 1, then: Joi.object({  // thông tin thay đổi
                businessLicenseUrl: Joi.string().uri().optional(),
                companyName: Joi.string().optional(),
                provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                detailAddress: Joi.string().required(),
                newProvinceCode: Joi.string().optional().allow(null, ''),
                newWardCode: Joi.string().optional().allow(null, ''),
            }).unknown(true).required(), otherwise: Joi.optional()}),
            representations: Joi.array().items(
                Joi.object({
                    fullName: Joi.string().required(),
                    position: Joi.string().required(),
                    dob: Joi.string().required(),
                    id: Joi.string().required(),
                    issueDate: Joi.string().required(),
                    issuePlace: Joi.string().required(),
                    phoneNumber: Joi.string().required()
                }).unknown(true).required()
            ).required(),
            businessLicenseUrl: Joi.string().required(),
            referralSource: Joi.string().required(),
            referralName: Joi.string().required(),
        }).unknown(true)
        const { error } = schema.validate(requestPayload)
        if (error) {
            console.log(`error: `, JSON.stringify(error))
            return { isValid: false, errorCode: error?.details?.[0]?.context?.key, errorMessage: error?.details?.[0].message }
        }
        return { isValid: true }
    }  
    
    async processTc1() {
        try {
            const misaBody = this.body;
            const validateSmeInfoResult = this.validateTc1(misaBody);
            if (!validateSmeInfoResult.isValid) {
                const errors = [{
                    errorCode: validateSmeInfoResult.errorCode,
                    errorMessage: validateSmeInfoResult.errorMessage
                }]
                return this.res.status(400).json(new BadRequestResponse(errors));
            }

            // validate body product_code
            const validBodyProductCodes = [
                PRODUCT_CODE.MISA.SME_MISA_HM_DIAMOND,
                PRODUCT_CODE.MISA.SME_MISA_HM_GOLD,
                PRODUCT_CODE.MISA.SME_MISA_HM_SILVER,
                PRODUCT_CODE.MISA.SME_MISA_HM_PLATINUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_PREMIUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_STANDARD_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_VIP_LS
            ];
            if (!validBodyProductCodes.includes(misaBody.productCode)) {
                return this.res.status(400).json(new BadRequestResponse([], `Mã sản phẩm hợp đồng tái cấp không thỏa mãn`));
            }
    
            // One ACTIVATED contract
            const oldLoanContract = await loanContractRepo.getLoanContractRefinance(misaBody.oldContractNumber, misaBody.taxId, CHANNEL.MC, PARTNER_CODE.MISA, CONTRACT_TYPE.CREDIT_LINE);
            if (!oldLoanContract) {
                return this.res.status(400).json(new BadRequestResponse([], `Không có hợp đồng hạn mức cũ thỏa mãn ACTIVATED`));
            }
    
            // validate created_date - check if contract was created within last 6 months
            const sixMonthsAfterCreate = new Date(
                new Date(oldLoanContract.created_date).getTime() + 6 * TIME_CONVERT.MONTHS_IN_MS
            );
            const now = new Date();
            if (now < sixMonthsAfterCreate) {
                return this.res.status(400).json(new BadRequestResponse([], `Hợp đồng hạn mức cũ được tạo trong vòng 6 tháng gần đây`));
            }

            // validate product_code
            const validProductCodes = [
                PRODUCT_CODE.MISA.SME_MISA_HM_DIAMOND,
                PRODUCT_CODE.MISA.SME_MISA_HM_GOLD,
                PRODUCT_CODE.MISA.SME_MISA_HM_SILVER,
                PRODUCT_CODE.MISA.SME_MISA_HM_PLATINUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_PREMIUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_STANDARD_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_VIP_LS
            ];
            if (!validProductCodes.includes(oldLoanContract.product_code)) {
                return this.res.status(400).json(new BadRequestResponse([], `Mã sản phẩm hợp đồng hạn mức cũ không thỏa mãn`));
            }
            
            let body = await masterdataService.convertEvfLov({ partnerCode: PARTNER_CODE.MISA, convertObject: misaBody });
            await super.genContractNumber();
            body.contractNumber = this.contractNumber;
            body.oldContractNumber = oldLoanContract.contract_number;
            this.oldContractNumber = oldLoanContract.contract_number;
            await this.convertTc1Body(body);
            
            // Create loanContract (Tc2) and lock oldContractNumber
            let transactionResult = false;
            const client = await this.poolWrite.connect(); // client for transaction
            try {
                await client.query('BEGIN');
                const insertLoanResult = await loanContractRepo.insertLoanContract(this.convertedBody, client); // Create loan contract (Tc2)
                if (!insertLoanResult) {
                    throw new Error(`Insert loan contract ${this.convertedBody.contract_number} failed`);
                }
                const updateBlockedResult = await loanContractRepo.updateFieldLoanContract( // lock oldContractNumber
                    this.convertedBody.old_contract_number,
                    'is_blocked',
                    LOCK_STATUS.LOCKED,
                    client
                );
                if (!updateBlockedResult) {
                    throw new Error(`Update old contract ${this.convertedBody.old_contract_number} blocked status failed`);
                }
                await client.query('COMMIT');
                transactionResult = true;
            } catch (error) {
                await client.query('ROLLBACK');
                console.error(`${this.convertedBody.contract_number} | createLoanContractTransaction | error:`, error);
                const responseBody = {
                    code: ERROR_CODE.INT_SERVER_ERROR,
                    message: `${this.convertedBody.contract_number} | createLoanContractTransaction | error`
                };
                loggingService.saveRequestV2(this.req.poolWrite, this.convertedBody, responseBody, this.convertedBody.contract_number, this.convertedBody.request_id, this.convertedBody.partner_code);
                return this.res.status(500).json(new ServerErrorResponse(null, responseBody.message));
            } finally {
                client.release();
            }
            
            if (transactionResult) {
                loggingRepo.saveWorkflow(MisaStep.TC2, this.convertedBody.status, this.contractNumber, 'system')
            }

            let loanCustomerData = { ...body };
            if (body.isChange == 1) {
                loanCustomerData = {
                    ...loanCustomerData,
                    companyName: body?.changedInfo?.companyName,
                    addressOnLicense: body?.changedInfo?.addressOnLicense,
                    provinceOnLicense: body?.changedInfo?.provinceCode,
                    districtOnLicense: body?.changedInfo?.districtCode,
                    wardOnLicense: body?.changedInfo?.wardCode,
                    detailOnLicense: body?.changedInfo?.detailAddress,
                    businessLicenseUrl: body?.changedInfo?.businessLicenseUrl,
                    //new address
                    newProvinceCode: body?.changedInfo?.newProvinceCode,
                    newWardCode: body?.changedInfo?.newWardCode,
                    oldInfo: JSON.stringify({
                        companyName: body?.companyName,
                        addressOnLicense: body?.addressOnLicense,
                        provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                        districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                        wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                        detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                        businessLicenseUrl: body?.businessLicenseUrl,
                        //new address
                        newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                        newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                    })
                }
            } else {
                loanCustomerData = {
                    ...loanCustomerData,
                    provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                    districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                    wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                    detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                    //new address
                    newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                    newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                }
            }

            await Promise.all([
                loanContractRepo.insertLoanCustomerRepresentations(this.contractNumber, body.registrationNumber, body.representations),
                sqlHelper.insertData(
                    `loan_customer`,
                    loanCustomerRepo.columns,
                    sqlHelper.generateValues(utils.convertCamelToSnake(loanCustomerData), loanCustomerRepo.columns)
                ),
                loggingRepo.saveWorkflow(MisaStep.TC1, STATUS.RECEIVEDTC1, this.contractNumber, 'system')
            ])

            // Check CIC B11T
            const cicBody = {
                contractNumber: this.contractNumber,
                persons: body.representations.map((rep) => ({
                    idNumber: rep.id,
                    otherIdNumber: rep.otherId,
                    fullName: rep.fullName,
                    address: ''
                })),
                stepCheck: CIC_STEP_CHECK.TC1,
            }

            antiFraudService.checkCicB11t(cicBody).then((respData) =>
                this.handleCicResult(this.contractNumber, respData)
            );
            
            try {
                await this.saveFiles(body, this.contractNumber);
            } catch (e) {
                console.error(e);
                console.log(`${this.contractNumber} | AF1 | saveFiles error: `, e);
            }
            const respData = {
                contractNumber: this.contractNumber,
                status: STATUS.RECEIVEDTC1
            }
            return this.res.status(200).json(new SuccessResponse(respData));
        } catch (e) {
            console.error(e)
            return this.res.status(500).json(new ServerErrorResponse());
        }
    }

    handleCicResult = async (contractNumber, cicResult) => {
        if (!cicResult?.decision) {
            await this.unlockOldContract(); // for MISA TC1
            throw new Error(`${contractNumber} | TC1 | check checkCicB11t error`)
        }
        if (cicResult?.decision && [STATUS.ELIGIBLE, STATUS.NOT_ELIGIBLE].includes(cicResult.decision)) {
            const { decision } = cicResult || {};
            await loanContractRepo.updateContractStatus(decision, contractNumber);
            await loggingRepo.saveWorkflow(MisaStep.TC1, decision, contractNumber, 'system');

            //handle callback misa here
            const isPass = decision === STATUS.ELIGIBLE ? true : false;
            if (!isPass) { // handle NOT_ELIGIBLE
                await this.unlockOldContract(); // for MISA TC1
            }
            await smeMisaService.callbackCicResult({
                step: smeMisaService.CIC_STEP_MISA_CALLBACK.TC1,
                contractNumber,
                isPass: isPass,
                loanEffectTime: cicResult?.nextTimeCanRequest ?? LMS_DATE()
            });
            return isPass;
        }
    }

    unlockOldContract = async () => {
        await loanContractRepo.updateContractLockStatus(LOCK_STATUS.ACTIVE, this.oldContractNumber);
    }
}

module.exports = {
    kovA1,
    MisaA1,
    kovGetScheme,
    checkRevenue,
    McAppA1,
    SuperAppA1,
    MisaAf1,
    FinvA1,
    MisaTc1,
}