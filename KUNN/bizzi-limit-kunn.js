const { BadRequestResponseV2, SuccessResponse } = require("../base/response");
const kunnRepo = require("../repositories/kunn-repo");
const documentRepo = require("../repositories/document");
const {
  createMultipleInvoicesWithDocument,
  getInvoicesWithDocuments,
  getInvoiceById,
  getInvoiceDocumentByInvoiceId,
  getInvoicesWithDocumentsForBZHM
} = require("../repositories/invoice-repo");
const uuid = require("uuid");
const { KUNN_STATUS, STATUS, WORKFLOW_STAGE } = require("../const/caseStatus");
const { saveStepLog, saveWorkflow } = require("../repositories/logging-repo");
const { SERVICE_NAME, KUNN_WORKFLOW, PARTNER_CODE, CIC_STEP_CHECK, DATE_FORMAT, } = require("../const/definition");
const { getRepresentationsByCustomer } = require("../repositories/loan-customer-representations-repo");
const { routing } = require("../services/workflow-router");
const { findByContractNumber } = require("../repositories/loan-contract-repo");
const BizziKunnLender = require("./bizzi-kunn-lender");
const BizziLimitKunnLender = require('./bizzi-limit-kunn-lender');
const { getLoanCustomer } = require("../repositories/loan-customer-repo");
const { getValueCodeMasterdataV2, getFullAddressNew } = require("../utils/masterdataService");
const { getLatestLenderChangeRequest, getLenderChangeRequestDetail, updateLenderChangeRequest, getLatestChangeRequestsByParent } = require("../repositories/lender-change-request-repo");
const {
  LENDER_REFERENCE_TYPE,
  LENDER_REQUEST_TYPE,
  OWNER_ID,
  SUB_INFO_TYPE,
  KUNN_TEMPLATE_DOCTYPE_SIGNED,
  SIGNED_TYPE,
  TABLE,
  STEP,
  KUNN_DOCS_BIZZ,
  KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM,
  KUNN_DOCS_BZHM,
  DOCUMENT_REFERENCE_TABLE
} = require("../const/variables-const");
const { checkS3FilesExist } = require("../upload_document/s3-service");
const sqlHelper = require("../utils/sqlHelper");
const moment = require("moment");
const loanCustomerShareholderRepo = require("../repositories/loan-customer-shareholders-repo");
const { numberToString } = require("../utils/numberToString");
const { formatCash, snakeToCamel } = require("../utils/helper");
const productService = require("../utils/productService");
const { getProductDetailByCodeApi } = require("../apis/product-api");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const invoiceRepo = require("../repositories/invoice-repo");
const crmService = require("../utils/crmService");
const { LMS_DATE, now, formatDate, isBeforeDate } = require("../utils/dateHelper");
const loggingRepo = require("../repositories/logging-repo");
const { checkAvailableAmountApi } = require("../apis/lms-api");
const s3Service = require("../upload_document/s3-service");
const { disbursementRequestSchema } = require("./schema/bizzi-limit-disbursement.schema");
const disbursementInfoRepo = require("../repositories/kunn-disbursement-info-repo");

async function validateContractNumber(contractNumber, partnerCode) {
  const loanContract = await findByContractNumber({ contractNumber, partnerCode });
  if (!loanContract) {
    throw new BadRequestResponseV2([`contract_number: ${contractNumber} not found`], `Loan contract not found`);
  }
  if (loanContract.status !== STATUS.ACTIVATED) {
    throw new BadRequestResponseV2([`contract_number: ${contractNumber} is not activated`], `Loan contract is not activated`);
  }
  const endDate = formatDate(loanContract.end_date, DATE_FORMAT.DB_FORMAT);
  const currentDate = formatDate(now(), DATE_FORMAT.DB_FORMAT);
  console.log(`[${contractNumber}] endDate: ${endDate}, currentDate: ${currentDate}`);
  if (isBeforeDate(endDate, currentDate)) {
    throw new BadRequestResponseV2([`${contractNumber}`], `Loan contract is expired`);
  }
  return loanContract;
}

async function validateDocTypes(body, productInfo, productCode) {
  const bundle = productInfo?.bundles;
  
  const disbursementBundles = bundle?.disbursementBundles;
  if (!disbursementBundles || disbursementBundles.length === 0) {
    throw new BadRequestResponseV2([], "No disbursement bundles found in product information");
  }
  const disbursementBundle = disbursementBundles.find((b) => b.bundleName === productCode);
  if (!disbursementBundle) {
    throw new BadRequestResponseV2([], `Disbursement bundle not found in product information`);
  }
  const docList = disbursementBundle?.docList;
  if (!docList || docList.length === 0) {
    throw new BadRequestResponseV2([], "No documents found in disbursement bundle");
  }
  const bundleDocumentTypes = docList.map((doc) => doc.docType);
  const documents = [];
  for(const disbursement of body.disbursements){
    if (disbursement.documents && Array.isArray(disbursement.documents)) {
      documents.push(...disbursement.documents);
    }

    if (disbursement.invoices && Array.isArray(disbursement.invoices)) {
      disbursement.invoices.forEach((invoice) => {
        if (invoice.files && Array.isArray(invoice.files)) {
          documents.push(...invoice.files);
        }
      });
    }
  }

  const invalidDocTypes = documents
    .map((doc) => doc.doc_type)
    .filter((docType) => !bundleDocumentTypes.includes(docType));
  const uniqueInvalidDocTypes = [...new Set(invalidDocTypes)];
  if (uniqueInvalidDocTypes.length > 0) {
    throw new BadRequestResponseV2(uniqueInvalidDocTypes, `Invalid document some document types`);
  }
  return documents;
}

async function validateFileExists(uploadedDocuments) {
  if (!uploadedDocuments.length) throw new BadRequestResponseV2([], "No files found in request body");
  const uploadedDocumentIds = uploadedDocuments.map((doc) => doc.doc_id);
  const isDuplicateDocs = new Set(uploadedDocumentIds).size !== uploadedDocumentIds.length;
  if (isDuplicateDocs) throw new BadRequestResponseV2([uploadedDocumentIds], "Duplicate docIds found in request body");

  const documents = await documentRepo.getDocumentsByDocIdsWhereNoContract(uploadedDocumentIds);
  const savedDocIds = documents.map((doc) => doc.doc_id);
  
  const notFoundDocIds = uploadedDocumentIds.filter((id) => !savedDocIds.includes(id));
  if (notFoundDocIds.length > 0) {
    throw new BadRequestResponseV2(notFoundDocIds, "Some files do not exists");
  }
  const invalidDocType = uploadedDocuments.filter((doc) => {
    const file = documents.find((f) => f.doc_id === doc.doc_id);
    return file && file.doc_type !== doc.doc_type;
  });
  
  if (invalidDocType.length > 0) {
    throw new BadRequestResponseV2(
      invalidDocType.map((doc) => ({ docId: doc.doc_id, docType: doc.doc_type })),
      "Some document types are invalid"
    );
  }

  const fileKeys = documents.map((doc) => doc.file_key);
  const fileExists = await checkS3FilesExist(fileKeys);
  if (!fileExists || typeof fileExists === "string") {
    const doc = documents.filter((doc) => fileExists.includes(doc.file_key))?.[0];
    throw new BadRequestResponseV2([{ doc_id: doc?.doc_id, doc_type: doc?.doc_type }], "Some files do not upload before");
  }
}

/**
 * Validate the disbursement request
 * 1. Check valid by Yup
 * 2. Check valid contract number
 * 3. Check processing KUNN status
 * 4. Check valid documents
 * 5. Check valid amount 
 * @param {*} body
 * @returns
 */
async function validateDisbursementRequest(body) {
  const { error, value } = disbursementRequestSchema.validate(body);
  if (error) {
    const errors = error.details.map((detail) => detail.message.replace(/"/g, ""));
    throw new BadRequestResponseV2(errors, "Invalid request body");
  }
  const [contract] = await Promise.all([
    validateContractNumber(value.contract_number, PARTNER_CODE.BZHM),
    checkProcessingKunn(body.contract_number),
  ]);
  const productInfo = await getProductDetailByCodeApi(contract.product_code);
  if (!productInfo) throw new BadRequestResponseV2([], "Cannot retrieve product information");

  const documents = await validateDocTypes(value, productInfo, contract.product_code);
  const [availableAmount] = await Promise.all([
    validateAmount(snakeToCamel(value), productInfo.kunn),
    validateFileExists(documents),
  ]);
  value.available_amount = availableAmount;
  value.contract_type = contract.contract_type;
  value.product_code = contract.product_code;
  value.tenor = productInfo.kunn?.tenor;
  value.documents = documents;
  return value;
}

async function saveContractDocuments(files, kunnId, contractNumber) {
  let insertPromises = [];
  for (const file of files) {
    let document = {
      contractNumber: contractNumber,
      docType: file.doc_type,
      docId: uuid.v4(),
      docGroup: null,
      fileKey: file.file_key,
      fileName: file.file_name,
      typeCollection: null,
      url: null,
      kunnContractNumber: kunnId,
      fileSize: null,
      disbursementInfoId: null,
      period: null,
    };
    insertPromises.push(documentRepo.insert(document));
  }
  await Promise.all(insertPromises);
}

async function saveMultiDisbursementInfo(disbursements) {
  for (const info of disbursements) {
    const result = await disbursementInfoRepo.save(info);
    const updatePromises = [];
    for (const document of info.documents) {
      updatePromises.push(documentRepo.updateReference({ docId: document.doc_id, docType: document.doc_type, referenceId: result.id, referenceTable: DOCUMENT_REFERENCE_TABLE.DISBURSEMENT_INFO }));
    }
    const debtId = `${info.kunnId}_${result.id}`;
    const invoices = info.invoices.map((invoice, index) => ({
      debt_contract_number: debtId,
      invoice_order: index,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      invoice_amount: invoice.invoice_amount,
      payment_date: info.paymentDate,
      note: invoice.note,
      is_invoice_valid: invoice.is_invoice_valid,
      files: invoice.files
    }));
    await createMultipleInvoicesWithDocument(invoices);
    if (updatePromises.length) {
      await Promise.all(updatePromises);
    }
  }
}

async function updateKunnDocument({ files, contractNumber, kunnContractNumber, signedType }) {
  let updatePromises = [];
  for (const file of files) {
    updatePromises.push(
      documentRepo.updateLoanContractNumberKunn({
        docType: file.doc_type,
        docId: file.doc_id,
        contractNumber,
        kunnContractNumber,
        signedType: signedType ?? null,
      })
    );
  }
  const results = await Promise.all(updatePromises);
  return results;
}

async function checkProcessingKunn(contractNumber) {
  const data = await kunnRepo.getKunnByContractNumber(contractNumber);
  const listKunn = data?.rows || [];
  const listStatusProcessingNotIn = [KUNN_STATUS.ACTIVATED, KUNN_STATUS.REFUSED, KUNN_STATUS.CANCELLED, KUNN_STATUS.NOT_ELIGIBLE, KUNN_STATUS.TERMINATED];
  if (listKunn && Array.isArray(listKunn) && listKunn.length > 0) {
    for (const kunn of listKunn) {
      if (!listStatusProcessingNotIn.includes(kunn.status)) {
        throw new BadRequestResponseV2([kunn?.kunn_id], `Has processing KUNN`);
      }
    }
  }
}

const validateAmount = async (payload, kunnConfig) => {
  const totalWithdrawalAmount = Number(payload.totalWithdrawalAmount);
  const disbursementAmount = payload.disbursements.reduce((sum, item) => sum + Number(item.amount), 0);
  if (totalWithdrawalAmount <= 0) throw new BadRequestResponseV2([], `Withdraw amount must be greater than 0`);
  if (totalWithdrawalAmount !== disbursementAmount) throw new BadRequestResponseV2([], `Total withdraw amount must be equal to total disbursement amount`);

  const lmsContract = await checkAvailableAmountApi(payload.contractNumber);
  if (!lmsContract?.avalibleAmount) throw new BadRequestResponseV2([], `Can not get avalible amount`);
  const availableAmount = Number(lmsContract.avalibleAmount);

  if (totalWithdrawalAmount > availableAmount) {
    throw new BadRequestResponseV2([], `Total amount in list invoices is not equal to total withdraw amount or exceeds available amount`);
  }

  if (Number(kunnConfig.minAmount) > totalWithdrawalAmount) {
    throw new BadRequestResponseV2([], `Total withdraw amount must be greater than or equal to minimum amount ${kunnConfig.minAmount}`);
  }
  return availableAmount;
};

/**
 * Create KUNN
 * 1. validate payload
 * 2. generate KUNN ID
 * 3. insert KUNN
 * 4. update documents
 * 5. save invoices
 * 5. start workflow
 * @param {*} req
 * @param {*} res
 * @returns
 */
async function requestDisbursement(req, res) {
  const payload = await validateDisbursementRequest(req.body);
  const kunnId = await kunnRepo.genKunnNumber();
  if (!kunnId) throw new BadRequestResponseV2([], "Failed to generate KUNN ID");
  
  const endDate = moment().add(payload.tenor, "months").format("yyyy-MM-DD");
  const startDate = moment().format("yyyy-MM-DD");

  const kunn = {
    ...payload,
    status: KUNN_STATUS.RECEIVED,
    kunn_id: kunnId,
    kunn_code: payload.product_code,
    available_amount: payload.available_amount,
    with_draw_amount: payload.total_withdrawal_amount,
    start_date: startDate,
    end_date: endDate,
    bill_day: payload.monthly_payment_date,
    tenor: Number(payload.tenor),
    lms_type: payload.contract_type,
  };

  const result = await kunnRepo.insertKunn(kunn);
  if (!result) throw new BadRequestResponseV2([], "Failed to insert KUNN data");
  const updateKunnDocumentPayload = { files: payload.documents, kunnContractNumber: kunnId, contractNumber: payload.contract_number };
  const disbursements = payload.disbursements.map((disbursement) => ({
    kunnId: kunnId,
    beneficiary: disbursement.account_name,
    accountName: disbursement.account_name,
    bankAccount: disbursement.account_number,
    bankCode: disbursement.bank_code,
    bankBranchCode: disbursement.branch_code,
    amount: disbursement.amount,
    paymentDate: startDate,
    transferContent: disbursement.transfer_content,
    bankName: disbursement.bank_name,
    invoices: disbursement.invoices,
    documents: disbursement.documents,
  }));
  await saveMultiDisbursementInfo(disbursements);
  await updateKunnDocument(updateKunnDocumentPayload);

  routing({
    currentTask: KUNN_WORKFLOW.START_KUNN,
    partnerCode: payload.partner_code,
    contractNumber: payload.contract_number,
    kunnId: kunnId,
  });
  return new SuccessResponse({ contract_number: payload.contract_number, debt_contract_number: kunnId, status: KUNN_STATUS.RECEIVED, error_code: "" }, "Request disbursement created successfully");
}

async function validateResubmitFiles(kunnId, body) {
  const { files } = body;
  const resubmitFiles = await documentRepo.getResubmitListKunn(kunnId);
  if (resubmitFiles.length > 0) {
    if (!files || !Array.isArray(files) || files.length === 0) {
      throw new BadRequestResponseV2([], "Files are required for resubmission");
    }
    const docTypes = resubmitFiles.map((file) => file.doc_type);
    const fileDocTypes = files.map((file) => file.doc_type);
    const missingDocTypes = docTypes.filter((docType) => !fileDocTypes.includes(docType));
    if (missingDocTypes.length > 0) {
      throw new BadRequestResponseV2(missingDocTypes, "Missing required document types for resubmission");
    }
    await validateFileExists(body);
  }
  return resubmitFiles;
}

async function validateResubmitInvoices(kunnId, body) {
  const { invoices } = body;
  const resubmitInvoices = await invoiceRepo.getInvoiceWaitingResubmit(kunnId);
  if (resubmitInvoices.length > 0) {
    if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
      throw new BadRequestResponseV2([], "Invoice details are required for resubmission");
    }
    const invoiceNumbers = resubmitInvoices.map((invoice) => invoice.invoice_number);
    const bodyInvoiceNumbers = invoices.map((invoice) => invoice.invoice_number);
    const missingInvoiceNumbers = invoiceNumbers.filter((num) => !bodyInvoiceNumbers.includes(num));
    if (missingInvoiceNumbers.length > 0) {
      throw new BadRequestResponseV2(missingInvoiceNumbers, "Missing required invoice numbers for resubmission");
    }
  }
  return resubmitInvoices;
}

async function resubmitDisbursement(req, res) {
  const { body } = req;
  const { debt_contract_number, contract_number } = req.params;
  await validateContractNumber(contract_number, PARTNER_CODE.BZHM);
  const resubmitFiles = await validateResubmitFiles(debt_contract_number, body);
  const resubmitInvoices = await validateResubmitInvoices(debt_contract_number, body);
  const value = body;
  const kunnId = debt_contract_number;
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunn_id is required for resubmission");
  }

  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.WAITING_RESUBMIT].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in waiting resubmit status");
  }

  const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT });
  const changeRequestDetail = await getLenderChangeRequestDetail(changeRequest?.id);
  if (!changeRequest || !changeRequestDetail) {
    throw new BadRequestResponseV2([], "Change request not found");
  }
  let savedBody = {
    status: KUNN_STATUS.RESUBMITED,
    updated_date: new Date(),
  };
  const changeRequestResubmit = Array.isArray(changeRequestDetail) ? changeRequestDetail.filter((item) => item.status === false && item.is_change === false) : [];
  if (changeRequestResubmit.length > 0) {
    const listChangeKey = changeRequestResubmit.map((item) => item.key);
    const keys = Object.keys(value);

    if (!listChangeKey.every((key) => keys.includes(key))) {
      const missingKeys = listChangeKey.filter((key) => !keys.includes(key));
      throw new BadRequestResponseV2(missingKeys, "Request body does not contain all required change keys");
    }

    let newValue = {};
    for (const key of listChangeKey) {
      if (value.hasOwnProperty(key)) {
        newValue[key] = value[key];
      }
    }

    savedBody = {
      ...savedBody,
      ...newValue,
    };
  }

  console.log(`Updated resubmited KUNN data: ${JSON.stringify(savedBody)}`);
  const columns = Object.keys(savedBody);
  const values = Object.values(savedBody);

  const isUpdated = await sqlHelper.updateData({
    table: TABLE.KUNN,
    columns: columns,
    values: values,
    conditions: { kunn_id: kunnId },
  });
  if (!isUpdated) {
    throw new BadRequestResponseV2([], "Failed to update KUNN data");
  }

  if (resubmitInvoices && resubmitInvoices.length > 0) {
    // Update invoices
    const invoices = value.invoices;
    //update deleted invoices
    for (const invoice of resubmitInvoices) {
      await invoiceRepo.deleteInvoiceByNumber(invoice.invoice_number, kunnId);
    }
    const invoicesWithKunnId = invoices.map((invoice) => ({
      ...invoice,
      debt_contract_number: kunnId,
      contract_number: contract_number,
    }));
    await createMultipleInvoicesWithDocument(invoicesWithKunnId);
  }
  //update deleted files
  if (resubmitFiles && resubmitFiles.length > 0) {
    for (const file of resubmitFiles) {
      await documentRepo.deleteDocument(file?.id);
    }
    //save documents
    const files = value.files;
    await saveContractDocuments(files, kunnId, contract_number);
  }
  //update change request status
  await updateLenderChangeRequest(changeRequest.id, {
    status: LENDER_CHANGE_REQUEST_STATUS.RESUBMITTED,
    meta: JSON.stringify(body),
  });

  //start again workflow
  const bodyRouting = {
    currentTask: KUNN_WORKFLOW.START_KUNN,
    partnerCode: value.partner_code,
    contractNumber: contract_number,
    kunnId: kunnId,
  };
  routing(bodyRouting);

  return new SuccessResponse({ contract_number: contract_number, debt_contract_number: kunnId, status: KUNN_STATUS.RESUBMITED, error_code: "" }, "Request disbursement resubmitted successfully");
}

async function searchKunnLender(req, res) {
  try {
    const { body } = req;
    body.partnerCodes = ["BZHM"];
    body.columns = ["kunn_id", "kunn_code", "partner_code", "contract_number", "beneficiary_name", "bank_account", "bank_code", "bank_branch_code", "total_receivable_amount", "created_date", "updated_date", "with_draw_amount", "status", "available_amount"];
    const result = await kunnRepo.searchKunn(body);
    return new SuccessResponse(result);
  } catch (error) {
    throw new BadRequestResponseV2([], error.message || "Failed to search KUNN");
  }
}

async function approveKunnLender(data, url) {
  const { kunnId } = data;
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.PASSED_TD1, KUNN_STATUS.RESUBMITED].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in valid status");
  }

  //check has sub-info change request
  let changeRequests = await getLatestChangeRequestsByParent(LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, kunnId);
  if (changeRequests && changeRequests.length > 0) {
    throw new BadRequestResponseV2([], "KUNN has sub-info change requests. Waiting for approval.");
  }

  changeRequests = await getLatestChangeRequestsByParent(LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT, kunnId);
  if (changeRequests && changeRequests.length > 0) {
    console.log(`KUNN ${kunnId} has sub-info change requests. Waiting for resubmission.`);
    await kunnRepo.updateKUStatus(kunnId, KUNN_STATUS.WAITING_RESUBMIT);
    return new SuccessResponse();
  }

  const updated = await kunnRepo.updateKUStatus(kunnId, KUNN_STATUS.PASSED_MANUAL_PROCESS);
  const bodyData = { ...data, action: "approve" };
  const response = { updated };
  await saveStepLog(kunnId, SERVICE_NAME.UI_REVIEW, KUNN_WORKFLOW.KUNN_MANUAL_REVIEW, bodyData, response, url);
  if (!updated) {
    throw new BadRequestResponseV2([], "Failed to approve KUNN");
  }
  //run workflow
  const bodyRouting = {
    currentTask: KUNN_WORKFLOW.KUNN_MANUAL_REVIEW,
    partnerCode: kunnData.partner_code,
    contractNumber: kunnData.contract_number,
    kunnId: kunnId,
  };
  routing(bodyRouting);
  return new SuccessResponse();
}

async function cancelKunnLender(data) {
  const { kunnId, comment } = data;
  if (!kunnId || !comment) {
    throw new BadRequestResponseV2([], "kunnId and comment is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.REJECTED_MANUAL_PROCESS, KUNN_STATUS.PASSED_TD1, KUNN_STATUS.RESUBMITED, KUNN_STATUS.SIGNED, KUNN_STATUS.RESUBMITTED_SIGNED].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in valid status");
  }
  //save step log
  saveStepLog(kunnId, SERVICE_NAME.UI_REVIEW, LENDER_REQUEST_TYPE.LENDER_KUNN_CANCEL, data, null, null);
  await kunnRepo.updateKUStatus(kunnId, KUNN_STATUS.CANCELLED);
  crmService.removeContract(global.config, kunnId);
  return new SuccessResponse({}, "KUNN cancelled successfully");
}

async function rejectKunnChangeRequest(data) {
  const { kunnId, comment } = data;
  if (!kunnId || !comment) {
    throw new BadRequestResponseV2([], "kunnId and comment is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.WAITING_APPROVE_CHANGE_REQUEST].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in valid status");
  }
  const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE });
  if (!changeRequest) {
    throw new BadRequestResponseV2([], "Failed to reject change request for KUNN. Change request not found");
  }
  //save step log
  saveStepLog(kunnId, SERVICE_NAME.UI_REVIEW, LENDER_REQUEST_TYPE.LENDER_KUNN_REJECT_CHANGE_REQ, data, null, null);

  await updateLenderChangeRequest(changeRequest.id, {
    status: LENDER_CHANGE_REQUEST_STATUS.REJECTED,
    comment: comment,
  });
  await kunnRepo.updateKUStatus(kunnId, KUNN_STATUS.REJECTED_MANUAL_PROCESS);
  return new SuccessResponse({}, "KUNN rejected change request successfully");
}

async function approveKunnChangeRequest(data) {
  const { kunnId } = data;
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.WAITING_APPROVE_CHANGE_REQUEST].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in valid status");
  }
  // const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE });
  // if (!changeRequest) {
  //     throw new BadRequestResponseV2([], "Failed to approve change request for KUNN. Change request not found");
  // }

  const partnerCode = kunnData.partner_code;
  const kunnLender = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: kunnData,
    partnerCode,
    table: TABLE.KUNN,
  });
  await kunnLender.approve(data);
  return new SuccessResponse({}, "KUNN approve change request successfully");
}

async function resubmitKunnLender(data, url) {
  const { kunnId } = data;
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const updated = await kunnRepo.updateKUStatus(kunnId, KUNN_STATUS.RESUBMIT);
  const bodyData = { ...data, action: "resubmit" };
  const response = { updated };
  await saveStepLog(kunnId, SERVICE_NAME.UI_REVIEW, KUNN_WORKFLOW.KUNN_MANUAL_REVIEW, bodyData, response, url);
  if (!updated) {
    throw new BadRequestResponseV2([], "Failed to resubmit KUNN");
  }
  return new SuccessResponse({ kunnId });
}

async function getKunnDetail(kunnId) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const [kunnData, documents, changeRequest] = await Promise.all([kunnRepo.getKunnData(kunnId), documentRepo.getKunnDocNotByEVFAndDocTypes(kunnId, KUNN_DOCS_BZHM), getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT] })]);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const changeRequestDetails = changeRequest ? await getLenderChangeRequestDetail(changeRequest.id) : [];

  const partnerCode = kunnData.partner_code;
  const kunnLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: kunnData,
    partnerCode,
    table: TABLE.KUNN,
    changeRequest,
    changeRequestDetails,
  });
  const result = await kunnLenderModel.bindingData();

  const documentLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: documents,
    partnerCode,
    table: "kunn_document",
  });
  const files = documentLenderModel.extendData();
  //set status again and new file_name, file_key (if change)
  if (files && Array.isArray(files) && files.length > 0) {
    await setFiles(files, LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT);
  }
  return new SuccessResponse({ status: kunnData?.status, info: result, files: files, available_amount: kunnData?.available_amount, with_draw_amount: kunnData?.with_draw_amount });
}

async function getKunnInvoices(kunnId) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const [kunnData, invoices] = await Promise.all([kunnRepo.getKunnData(kunnId), getInvoicesWithDocuments(kunnId)]);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const partnerCode = kunnData.partner_code;
  const invoiceLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: invoices,
    partnerCode,
    table: "invoice",
  });
  const columns = await invoiceLenderModel.columnConfig;
  const data = invoiceLenderModel.extendData();
  if (data && Array.isArray(data) && data.length > 0) {
    for (const invoice of data) {
      invoice.status = invoice.waiting_resubmit !== true;
      const files = invoice.files || [];
      await setFiles(files, LENDER_REFERENCE_TYPE.INVOICE_DOCUMENT);
    }
  }

  return new SuccessResponse({ invoices: data, columns });
}

async function saveKunn(body, url) {
  const { kunnId } = body;
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.PASSED_TD1, KUNN_STATUS.RESUBMITED, KUNN_STATUS.REJECTED_MANUAL_PROCESS].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not in valid status");
  }
  const partnerCode = kunnData.partner_code;
  const kunnLender = await BizziLimitKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: kunnData,
    partnerCode,
    table: TABLE.KUNN,
    url: url,
  });
  await kunnLender.process(body);
  return new SuccessResponse({ message: "KUNN saved successfully" });
}

async function getLoanContractByKunn(kunnId) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const partnerCode = kunnData.partner_code;
  const contractNumber = kunnData.contract_number;
  const loanContract = await findByContractNumber({ contractNumber, partnerCode });
  if (!loanContract) {
    throw new BadRequestResponseV2([], "Loan contract not found for the given KUNN");
  }
  const [representations, loanCustomer, headquartersFullAddress] = await Promise.all([getRepresentationsByCustomer(contractNumber), getLoanCustomer(contractNumber), getFullAddressNew(loanContract.sme_headquarters_province, loanContract.sme_headquarters_ward)]);
  const addressPrefix = loanContract?.sme_headquarters_address ? `${loanContract.sme_headquarters_address}, ` : "";
  loanCustomer.headquarters_full_address = `${addressPrefix}${headquartersFullAddress}`;
  loanContract.representations = representations;
  loanContract.loan_customer = loanCustomer;

  return new SuccessResponse(loanContract);
}

async function saveUploadDocument({ kunnId, files }) {
  if (!kunnId || !files || files.length === 0) {
    throw new BadRequestResponseV2([], "kunnId and files are required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const contractNumber = kunnData.contract_number;
  let promises = [];

  //check all files exist in S3
  const fileKeys = files.map((file) => file.fileKey);
  const fileExists = await checkS3FilesExist(fileKeys);
  if (!fileExists || typeof fileExists === "string") {
    throw new BadRequestResponseV2([fileExists], "Some files do not exist in S3");
  }

  for (const file of files) {
    if (!file.fileKey || !file.docType) {
      throw new BadRequestResponseV2([], "file_key and doc_type are required for each file");
    }
    //check if file already exists in loan contract documents
    const existingDoc = await documentRepo.findByKunnDocTypeFileKey({ kunn_contract_number: kunnId, doc_type: file.docType, file_key: file.fileKey });
    if (existingDoc) {
      throw new BadRequestResponseV2([file.fileKey], `Document with type ${file.docType} and file key ${file.fileKey} already exists for KUNN ${kunnId}`);
    }

    // Extract file name from file_key
    let fileKey = file.fileKey;
    let fileName = fileKey ? fileKey.split("/").pop() : null;
    let document = {
      contract_number: contractNumber,
      doc_type: file.docType,
      doc_id: uuid.v4(),
      kunn_contract_number: kunnId,
      owner_id: OWNER_ID.EVF,
      file_key: fileKey,
      file_name: fileName,
      request_id: uuid.v4(),
    };
    promises.push(documentRepo.insertLoanContractDocument(document));
  }
  const data = await Promise.all(promises);
  if (data.length === 0) {
    throw new BadRequestResponseV2([], "Failed to save documents");
  }
  const savedFiles = data.map((doc) => ({
    docType: doc.doc_type,
    fileKey: doc.file_key,
    fileName: doc.file_name,
    docId: doc.doc_id,
  }));
  return new SuccessResponse({ savedFiles }, "Save document successfully");
}

async function deleteDocument({ kunnId, docId, updatedBy }) {
  if (!kunnId || !docId) {
    throw new BadRequestResponseV2([], "kunnId and docId are required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const document = await documentRepo.findByDocID(docId);
  if (!document || document.kunn_contract_number !== kunnId) {
    throw new BadRequestResponseV2([], "Document not found for this KUNN");
  }
  const deleted = await documentRepo.deleteLoanContractDocument({ id: document.id, kunnId: kunnId, updatedBy: updatedBy });
  if (!deleted) {
    throw new BadRequestResponseV2([], "Failed to delete document");
  }
  return new SuccessResponse({ docId }, "Document deleted successfully");
}

async function getRelationDocuments(kunnId, ownerId = OWNER_ID.EVF) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const documents = await documentRepo.findDocumentByOwner({ kunnId, ownerId });
  return new SuccessResponse(documents);
}

async function setFiles(files, referenceType) {
  const promises = files.map((file) => {
    return getValueCodeMasterdataV2(file.doc_type, "DOCUMENT");
  });
  const docsMasterData = await Promise.all(promises);

  for (const file of files) {
    const fileChangeRequest = await getLatestLenderChangeRequest({ reference_code: file.id, reference_type: referenceType, status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE] });
    let fileChangeRequestDetails = [];
    if (fileChangeRequest) {
      fileChangeRequestDetails = await getLenderChangeRequestDetail(fileChangeRequest.id);
    }

    const docValue = docsMasterData.find((doc) => doc.code === file.doc_type);
    if (docValue) {
      file.doc_name_vn = docValue.nameVn ?? "";
    }
    file.status = file.waiting_resubmit === 1 ? false : true;
    for (const detail of fileChangeRequestDetails) {
      if (file[detail.key] !== undefined && detail.is_change) {
        file[detail.key] = detail.new_value;
      }
    }
  }
}

async function getSubInfo({ kunnId, subType, referenceId }) {
  if (!referenceId || !subType || !kunnId) {
    throw new BadRequestResponseV2([], "referenceId, subType and kunnId are required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const partnerCode = kunnData.partner_code;

  let tableName = "";
  let data = {};
  let files = [];
  let changeRequest, changeRequestDetails;
  switch (subType) {
    case SUB_INFO_TYPE.INVOICE:
      tableName = "invoice";
      const [invoice, invoiceFiles] = await Promise.all([getInvoiceById(referenceId), getInvoiceDocumentByInvoiceId(referenceId)]);
      data = invoice;
      files = invoiceFiles;
      changeRequest = await getLatestLenderChangeRequest({ reference_code: referenceId, reference_type: LENDER_REFERENCE_TYPE.INVOICE, status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT] });
      changeRequestDetails = changeRequest ? await getLenderChangeRequestDetail(changeRequest.id) : [];
      break;
    default:
      throw new BadRequestResponseV2([], "Invalid subType");
  }

  const subInfoLenderModel = await BizziKunnLender.init({
    referenceNumber: referenceId,
    data: data,
    partnerCode,
    table: tableName,
    changeRequest,
    changeRequestDetails,
  });
  if (files && Array.isArray(files) && files.length > 0) {
    await setFiles(files, LENDER_REFERENCE_TYPE.INVOICE_DOCUMENT);
  }
  const result = await subInfoLenderModel.bindingData();
  return new SuccessResponse({ info: result, files, subType: subType, referenceId: referenceId });
}

//processSubInfo
async function processSubInfo(body) {
  const { kunnId, subType, referenceId, info } = body;
  if (!kunnId || !subType || !referenceId || !info) {
    throw new BadRequestResponseV2([], "kunnId, subType, referenceId and info are required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }

  let data = {};
  let tableName = "";
  switch (subType) {
    case SUB_INFO_TYPE.INVOICE:
      tableName = "invoice";
      data = await getInvoiceById(referenceId);
      break;
    default:
      throw new BadRequestResponseV2([], "Invalid subType");
  }

  const partnerCode = kunnData.partner_code;
  const kunnLender = await BizziKunnLender.init({
    referenceNumber: referenceId,
    data: data,
    partnerCode,
    table: tableName,
  });
  await kunnLender.processSubInfo(body);
  return new SuccessResponse({ message: "Subinfo saved successfully" });
}

async function getKunnDocumentSigned(kunnId) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  // if (![KUNN_STATUS.SIGNED, KUNN_STATUS.RESUBMITTED_SIGNED, KUNN_STATUS.WAITING_RESUBMIT_SIGNED].includes(kunnData.status)) {
  //     throw new BadRequestResponseV2([], "KUNN is not signed");
  // }

  const contractSigned = await documentRepo.getDocumentsByKunnAndDocTypes(kunnId, KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM);
  if (!contractSigned) {
    throw new BadRequestResponseV2([], "Contract signed document not found for this KUNN");
  }
  const promises = KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM.map((docType) => {
    return getValueCodeMasterdataV2(docType, "DOCUMENT");
  });
  const docs = await Promise.all(promises);
  for (const signed of contractSigned) {
    const docType = signed.doc_type;
    const docValue = docs.find((doc) => doc?.code === docType);
    if (docValue) {
      signed.doc_name_vn = docValue.nameVn ?? "";
    }
  }

  const partnerCode = kunnData.partner_code;
  const fileLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnId,
    data: contractSigned,
    partnerCode,
    table: "kunn_document",
  });
  const data = fileLenderModel.extendData();
  //set status again
  for (const item of data) {
    item.status = item.waiting_resubmit !== 1;
  }
  return new SuccessResponse(data);
}

async function getDataForTemplateBTTBCTD(contractNumber) {
  const contract = await findByContractNumber({ contractNumber, partnerCode: PARTNER_CODE.BZHM });
  const cicLog = await sqlHelper.findOne({ table: "loan_cic_log", whereCondition: { contract_number: contractNumber, step: CIC_STEP_CHECK.AF2_DETAIL } });
  const cicResponse = JSON.parse(cicLog?.response_payload || "{}");
  const cicData = cicResponse?.cicData;
  const shareholders = await loanCustomerShareholderRepo.findByContractNumber(contractNumber);
  // Get revenue documents for the contractNumber with doc_type 'SFSTD'
  const reportTypeResult = (
    await global.poolRead.query(
      `
        SELECT financial_report_type
        FROM loan_revenues lr
        INNER JOIN revenue_documents rd ON lr.id = rd.loan_revenues_id
        WHERE lr.contract_number = $1
            AND rd.doc_type = 'SFSTD'
        ORDER BY lr.year DESC
        LIMIT 1
    `,
      [contractNumber]
    )
  )?.rows?.[0];
  const ratingResult = await sqlHelper.findOne({ table: "loan_rating", whereCondition: { contract_number: contractNumber } });
  const ratingDetails = JSON.parse(ratingResult?.detail_data || "{}");

  const fse =
    (
      await global.poolRead.query(
        `
        select * from financial_statements_export fse where contract_number = $1 and template not like '%LCTT%';
    `,
        [contractNumber]
      )
    )?.rows?.map((x) => x.id) || [];
  const fsDetails =
    (
      await global.poolRead.query(
        `
        select code, num_of_first_year, num_of_second_year from financial_statement_details fsd where financial_statements_export_id = ANY($1);
    `,
        [fse]
      )
    )?.rows || [];
  const ct = {};
  for (const item of fsDetails) {
    ct[`ct${item.code}_end`] = item.num_of_first_year;
    ct[`ct${item.code}_start`] = item.num_of_second_year;
  }
  const partners = await sqlHelper.find({ table: "loan_customer_partners", whereCondition: { contract_number: contractNumber } });
  const productInfo = await productService.getProductInfo(global.config, contract?.product_code);

  const now = moment();
  const contract_data = {
    ...contract,
    cic: parseCicData(cicData) || {},
    model: {
      ...ratingDetails?.finance_metric_details,
      ...ratingDetails?.non_finance_metric_details,
    },
    ct: ct,
    partner_in: partners
      .filter((x) => x.partner_type === "IN")
      .map((x) => ({
        tax_id: x.tax_id,
        company_name: x.company_name,
      })),
    partner_out: partners
      .filter((x) => x.partner_type === "OUT")
      .map((x) => ({
        tax_id: x.tax_id,
        company_name: x.company_name,
      })),
    shareholders: shareholders?.map((x, index) => ({
      idx: index + 1,
      name: x.fullName,
      ratio: x.capitalContributionRatio ?? 0,
    })),
    ngay: now.format("DD"),
    thang: now.format("MM"),
    nam: now.format("YYYY"),
    request_amt: formatCash(contract?.request_amt || 0),
    request_amt_text: numberToString(contract?.request_amt)?.replace(/ {2}/g, " ") || "",
    revenue_change_text: ct.ct01_end >= ct.ct01_start ? "tăng " : "giảm " + formatCash(Math.abs(ct.ct01_end - ct.ct01_start)),
    revenue_change_rate: ct.ct01_start !== 0 ? ((Math.abs(ct.ct01_end - ct.ct01_start) / ct.ct01_start) * 100).toFixed(2) + "%" : "0%",
    revenue_profit_text: ct.ct60_end >= ct.ct60_start ? "tăng " : "giảm " + formatCash(Math.abs(ct.ct60_end - ct.ct60_start)),
    revenue_profit_rate: ct.ct60_start !== 0 ? ((Math.abs(ct.ct60_end - ct.ct60_start) / ct.ct60_start) * 100).toFixed(2) + "%" : "0%",
    last_3_month_sales_anchor_avg: formatCash((contract?.last_3_month_sales_anchor || 0) / 3),
    last_3_month_sales_anchor_avg_x2: formatCash(((contract?.last_3_month_sales_anchor || 0) / 3) * 2),
    credit_limit: formatCash(ratingResult?.approved_limit || 0),
    credit_limit_text: numberToString(ratingResult?.approved_limit || 0)?.replace(/ {2}/g, " ") || "",
    repayment_period: productInfo?.factoringTermDays,
    prepayment: Number(productInfo?.advanceRate) || null,
    int_rate: Number(productInfo?.productVar?.[0]?.intRate) || null,
    fee: Number(productInfo?.fee?.[0]?.feeDetail?.[0]?.calculaDetail?.[0]?.value) || null,
  };
  for (const key in contract_data.model) {
    let obj = contract_data.model[key];
    contract_data.model[key] = {
      metric: obj.metric_score,
      ratio: obj.ratio,
      value: obj.score,
    };
  }
  const uniqueStr = uuid.v4().replace(/-/g, "").slice(0, 8);
  const task_request = {
    task_name: "GEN_BIZZ_BCTD",
    contract_number: contractNumber,
    doc_type: "BTTBCTD",
    callback_url: false,
    body: {
      doc_type: "BTTBCTD",
      file_name: `${moment().format("YYYYMMDD")}_${uniqueStr}_${contractNumber}_BTTBCTD.pdf`,
      contract_number: contractNumber,
      file_tem_path: `./template/BIZZ/BCTD-${reportTypeResult?.financial_report_type}.docx`,
      contract_data: contract_data,
    },
  };
  return task_request;
}

function parseCicData(cicData) {
  if (!cicData) return {};
  const cicDataCompany = cicData?.enterprises?.[0]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT;
  const cicDataPeople = cicData?.persons
    ?.map((item) => {
      let obj = item.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT;
      if (obj) {
        obj.name = item.customerName;
      }
      return obj;
    })
    .filter((x) => !!x);

  function toArray(obj) {
    if (Array.isArray(obj)) {
      return obj;
    } else {
      return obj ? [obj] : [];
    }
  }

  const docData = {
    company: {
      QHTD: {
        DONG:
          toArray(cicDataCompany?.QHTD?.DONG || []).map((item) => {
            let rows = toArray(item?.CTLOAIVAY?.DONG || []);
            let listLoaiVay = new Set(rows.map((cv) => cv?.LOAIVAY || ""));
            let CTLOAIVAY = [...listLoaiVay].map((lv) => {
              let rows2 = rows.filter((cv) => cv?.LOAIVAY === lv);
              return {
                LOAIVAY: lv,
                NHOMNO: rows2,
                TONG_VND: rows2.reduce((acc, cv) => acc + Number(cv?.DUNO_VND || 0), 0),
                TONG_USD: rows2.reduce((acc, cv) => acc + Number(cv?.DUNO_USD || 0), 0),
              };
            });
            return {
              ...item,
              CTLOAIVAY,
            };
          }) || [],
      },
      DUNO_THETD: toArray(cicDataCompany?.DUNO_THETD?.DONG || []).reduce(
        (acc, item) => {
          acc.TONG_HANMUC += Number(item?.HANMUC_THETD || 0);
          acc.SOTIEN_PHAI_TT += Number(item?.SOTIEN_PHAI_TT || 0);
          acc.SOTIEN_CHAM_TT += Number(item?.SOTIEN_CHAM_TT || 0);
          acc.SOLUONG_THETD += Number(item?.SOLUONG_THE_TD || 0);
          return acc;
        },
        {
          TONG_HANMUC: 0,
          SOTIEN_PHAI_TT: 0,
          SOTIEN_CHAM_TT: 0,
          SOLUONG_THETD: 0,
        }
      ),
      CAMKETNB: cicDataCompany?.CAMKETNB?.DONG || [],
    },
    persons: {
      DONG: cicDataPeople?.map((x) => {
        return {
          name: x.name,
          ...toArray(x.QHTD?.DONG).reduce(
            (acc, item) => {
              const rows = toArray(item?.CTLOAIVAY?.DONG || []);
              acc.TONG_VND += rows.reduce((sum, cv) => sum + Number(cv?.DUNO_VND || 0), 0);
              acc.TONG_USD += rows.reduce((sum, cv) => sum + Number(cv?.DUNO_USD || 0), 0);
              const maxNhomNo = Math.max(...rows.map((cv) => Number(cv?.NHOMNO || 0)), 0);
              acc.MAX_NHOMNO = Math.max(acc.MAX_NHOMNO, maxNhomNo);
              return acc;
            },
            { TONG_VND: 0, TONG_USD: 0, MAX_NHOMNO: 0 }
          ),
          ...toArray(x.DUNO_THETD?.DONG).reduce(
            (acc, item) => {
              acc.HANMUC_THETD += Number(item?.HANMUC_THETD || 0);
              acc.SOTIEN_PHAI_TT += Number(item?.SOTIEN_PHAI_TT || 0);
              return acc;
            },
            { HANMUC_THETD: 0, SOTIEN_PHAI_TT: 0 }
          ),
        };
      }),
    },
  };
  docData.company.QHTD.TONG_VND = docData.company.QHTD.DONG.reduce((acc, item) => {
    return acc + Number(item?.TONG_VND || 0);
  }, 0);
  docData.company.QHTD.TONG_USD = docData.company.QHTD.DONG.reduce((acc, item) => {
    return acc + Number(item?.TONG_USD || 0);
  }, 0);
  docData.persons.TONG_VND = docData.persons.DONG.reduce((acc, item) => {
    return acc + Number(item?.TONG_VND || 0);
  }, 0);
  docData.persons.TONG_USD = docData.persons.DONG.reduce((acc, item) => {
    return acc + Number(item?.TONG_USD || 0);
  }, 0);
  docData.persons.HANMUC_THETD = docData.persons.DONG.reduce((acc, item) => {
    return acc + Number(item?.HANMUC_THETD || 0);
  }, 0);
  docData.persons.SOTIEN_PHAI_TT = docData.persons.DONG.reduce((acc, item) => {
    return acc + Number(item?.SOTIEN_PHAI_TT || 0);
  }, 0);
  return docData;
}

async function processKunnSignedDocument({ kunnId, files, updatedBy }) {
  if (!kunnId || !files || files.length === 0) {
    throw new BadRequestResponseV2([], "kunnId and files are required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (![KUNN_STATUS.SIGNED, KUNN_STATUS.RESUBMITTED_SIGNED].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN status is not valid");
  }
  const listResubmits = files.filter((file) => file.status === false);
  for (const file of files) {
    if (!file.doc_id || !file.doc_type) {
      throw new BadRequestResponseV2([], "doc_id and doc_type are required for each file");
    }
  }

  //update status to WAITING_EVF_SIGNED for reject duplicate request
  await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_EVF_SIGNED);

  for (const file of files) {
    if (!file.doc_id || !file.doc_type) {
      throw new BadRequestResponseV2([], "doc_id and doc_type are required for each file");
    }
    if (!KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM.includes(file.doc_type)) {
      console.log(`[processKunnSignedDocument] Invalid doc_type: ${file.doc_type} signed for KUNN ${kunnId}`);
      continue;
    }
    // Check if file already exists
    const existingDoc = await documentRepo.findByKunnDocIdAndType({ kunnId, docId: file.doc_id, docType: file.doc_type });
    if (existingDoc) {
      await documentRepo.saveKunnDocumentChecked({
        kunnId,
        docType: file.doc_type,
        docId: file.doc_id,
        status: file.status,
        comment: file.comment,
        updatedBy,
      });
    }
  }
  if (listResubmits.length > 0) {
    await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_RESUBMIT_SIGNED);
    //callback to resubmit for partner
  } else {
    //All documents are checked
    const allChecked = await areAllSignedDocumentsChecked(kunnId);
    if (allChecked) {
      //routing and go to next step: SIGN_EVF_SIGNATURE_KUNN
      const bodyRouting = {
        currentTask: KUNN_WORKFLOW.KUNN_SIGNED,
        partnerCode: kunnData.partner_code,
        contractNumber: kunnData.contract_number,
        kunnId: kunnId,
      };
      routing(bodyRouting);
    }
  }
  return new SuccessResponse({}, "Signed documents processed successfully");
}

async function areAllSignedDocumentsChecked(kunnId) {
  if (!kunnId) return false;
  const docs = await documentRepo.getDocumentsByKunnAndDocTypes(kunnId, KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM);
  if (!docs || docs.length === 0) return false;
  // Each doc must have status explicitly true or false (not null/undefined)
  return KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM.every((docType) => {
    const doc = docs.find((d) => d.doc_type === docType);
    return doc && doc.is_checked === 1 && doc.is_ss_checked === 1;
  });
}

async function validateSubmitSignedDocument({ debt_contract_number, files, type = "SUBMIT" }) {
  if (!debt_contract_number || !Array.isArray(files) || files.length === 0) {
    throw new BadRequestResponseV2([], "debt_contract_number and files are required");
  }
  const kunnData = await kunnRepo.getKunnData(debt_contract_number);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (type === "SUBMIT" && kunnData.status !== KUNN_STATUS.SIGNING_IN_PROGRESS) {
    throw new BadRequestResponseV2([], "KUNN is not signing in progress");
  }

  if (type === "RESUBMIT" && ![KUNN_STATUS.WAITING_RESUBMIT_SIGNED].includes(kunnData.status)) {
    throw new BadRequestResponseV2([], "KUNN is not waiting for resubmit signed documents");
  }

  const missingDocTypes = KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM.filter((docType) => !files.some((file) => file.doc_type === docType));
  if (type === "SUBMIT" && missingDocTypes.length > 0) {
    throw new BadRequestResponseV2(missingDocTypes, `Missing signed document types: ${missingDocTypes.join(", ")}`);
  }

  await validateFileExists(files);
}

async function submitKunnSignedDocument({ debt_contract_number, files }) {
  await validateSubmitSignedDocument({ debt_contract_number, files });
  const kunnData = await kunnRepo.getKunnData(debt_contract_number);
  let softDeletePromises = [];
  for (const file of files) {
    softDeletePromises.push(
      documentRepo.deleteLoanContractDocument({
        kunnId: debt_contract_number,
        docType: file.doc_type,
        updatedBy: "system",
      })
    );
  }
  await Promise.all(softDeletePromises);
  await updateKunnDocument({ files, contractNumber: kunnData.contract_number, kunnContractNumber: debt_contract_number, signedType: SIGNED_TYPE.PARTNER_SIGNED });
  await kunnRepo.update(debt_contract_number, {
    status: KUNN_STATUS.SIGNED,
    signature_date: new Date(),
  });
  return new SuccessResponse({}, "Signed documents submitted successfully");
}

async function validateResubmitSignedDocument({ debt_contract_number, files }) {
  if (!debt_contract_number || !Array.isArray(files) || files.length === 0) {
    throw new BadRequestResponseV2([], "debt_contract_number and files are required");
  }
  const signedDocument = await documentRepo.getDocumentsByKunnAndDocTypes(debt_contract_number, KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM);
  const resubmitDocument = signedDocument.filter((doc) => doc.waiting_resubmit === 1 && doc.is_ss_checked === 1);
  if (resubmitDocument.length === 0) {
    throw new BadRequestResponseV2([], "No signed documents waiting for resubmit");
  }
  const missingDocTypes = resubmitDocument.filter((doc) => !files.some((file) => file.doc_type === doc.doc_type));
  if (missingDocTypes.length > 0) {
    throw new BadRequestResponseV2(
      missingDocTypes.map((doc) => doc.doc_type),
      `Missing signed document types: ${missingDocTypes.map((doc) => doc.doc_type).join(", ")}`
    );
  }
  const notResubmitDocTypes = files.filter((file) => !resubmitDocument.some((doc) => doc.doc_type === file.doc_type));
  if (notResubmitDocTypes.length > 0) {
    throw new BadRequestResponseV2(
      notResubmitDocTypes.map((file) => file.doc_type),
      `These document types are not waiting for resubmit: ${notResubmitDocTypes.map((file) => file.doc_type).join(", ")}`
    );
  }
}

async function reSubmitKunnSignedDocument({ debt_contract_number, files }) {
  await validateSubmitSignedDocument({ debt_contract_number, files, type: "RESUBMIT" });
  await validateResubmitSignedDocument({ debt_contract_number, files });
  const kunnData = await kunnRepo.getKunnData(debt_contract_number);
  let softDeletePromises = [];
  for (const file of files) {
    softDeletePromises.push(
      documentRepo.deleteLoanContractDocument({
        kunnId: debt_contract_number,
        docType: file.doc_type,
        updatedBy: "system",
      })
    );
  }
  await Promise.all(softDeletePromises);
  await updateKunnDocument({ files, contractNumber: kunnData.contract_number, kunnContractNumber: debt_contract_number, signedType: SIGNED_TYPE.PARTNER_SIGNED });
  await kunnRepo.updateKUStatus(debt_contract_number, KUNN_STATUS.RESUBMITTED_SIGNED);
  return new SuccessResponse({}, "Resubmit signed documents successfully");
}

async function buildDataCallbackKunnResubmit(kunnId) {
  let data = {};
  const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT });
  if (changeRequest) {
    const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);
    if (changeRequestDetails && changeRequestDetails.length > 0) {
      for (const detail of changeRequestDetails) {
        if (detail.status === false && detail.is_change === false) {
          data[detail.key] = {
            value: detail.old_value || "",
            comment: detail.comment || "",
          };
        }
      }
    }
  }
  //files
  const files = await documentRepo.getResubmitListKunn(kunnId);
  if (files && files.length > 0) {
    data.files = files.map((file) => ({
      doc_id: file.doc_id,
      doc_type: file.doc_type,
      file_key: file.file_key,
      comment: file.comment,
    }));
  }

  //invoices
  const invoices = await invoiceRepo.getInvoiceWaitingResubmit(kunnId);
  let invoiceDetails = [];
  if (invoices && invoices.length > 0) {
    invoiceDetails = invoices.map((invoice) => {
      return {
        invoice_order: invoice.invoice_order,
        invoice_number: invoice.invoice_number,
        invoice_date: invoice.invoice_date,
        invoice_amount: invoice.invoice_amount,
        payment_date: invoice.payment_date,
        note: invoice.note,
        is_invoice_valid: invoice.is_invoice_valid,
        purchaser_tax_code: invoice.purchaser_tax_code,
        comment: invoice.comment || "",
      };
    });
  }
  // invoices change_request_details
  const changeRequestInvoices = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.INVOICE, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT });
  if (changeRequestInvoices) {
    const changeRequestInvoiceDetails = await getLenderChangeRequestDetail(changeRequestInvoices.id);
    if (changeRequestInvoiceDetails && changeRequestInvoiceDetails.length > 0) {
      const invoice = await invoiceRepo.getInvoiceById(Number(changeRequestInvoices.reference_code));
      const isExist = invoiceDetails.some((detail) => detail.invoice_number === invoice.invoice_number);
      if (invoice && !isExist) {
        invoiceDetails.push({
          invoice_order: invoice.invoice_order,
          invoice_number: invoice.invoice_number,
          invoice_date: invoice.invoice_date,
          invoice_amount: invoice.invoice_amount,
          payment_date: invoice.payment_date,
          note: invoice.note,
          is_invoice_valid: invoice.is_invoice_valid,
          purchaser_tax_code: invoice.purchaser_tax_code,
          comment: invoice.comment || "",
        });
      }
    }
  }
  data.invoices = invoiceDetails;
  return data;
}

async function buildDataCallbackKunnSignInProgress(contractNumber, kunnId) {
  const listdoc = await documentRepo.getDocumentsByContractAndKunnAndDocTypes(contractNumber, kunnId, KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM);
  if (!listdoc || listdoc.length === 0) {
    return [];
  }

  const result = await s3Service.genMultiplePresignedDownloadUrlForSme(listdoc);
  if (!result) {
    return [];
  }
  return result;
}

async function buildDataCallbackKunnResubmitSigned(kunnId) {
  const files = await documentRepo.getResubmitListKunn(kunnId);
  if (files && files.length > 0) {
    data.files = files.map((file) => ({
      doc_id: file.doc_id,
      doc_type: file.doc_type,
      file_key: file.file_key,
      comment: file.comment,
    }));
  }
  return files;
}

async function getKunnStatus({ contract_number, debt_contract_number }) {
  if (!contract_number && !debt_contract_number) {
    throw new BadRequestResponseV2([], "contract_number or debt_contract_number is required");
  }
  const kunnData = await kunnRepo.getKunnData(debt_contract_number);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  if (kunnData.contract_number !== contract_number) {
    throw new BadRequestResponseV2([], "contract_number does not match with KUNN data");
  }
  let data = {};
  let note = "";
  switch (kunnData.status) {
    case KUNN_STATUS.WAITING_RESUBMIT:
      note = "KUNN is waiting for resubmit";
      data = await buildDataCallbackKunnResubmit(debt_contract_number);
      break;
    case KUNN_STATUS.SIGNING_IN_PROGRESS:
      note = "KUNN is signing in progress";
      data.files = await buildDataCallbackKunnSignInProgress(contract_number, debt_contract_number);
      break;
    case KUNN_STATUS.WAITING_RESUBMIT_SIGNED:
      note = "KUNN is waiting for resubmit signed";
      data.files = await buildDataCallbackKunnResubmitSigned(debt_contract_number);
      break;
    default:
      break;
  }
  return new SuccessResponse({
    contract_number: kunnData.contract_number,
    debt_contract_number: kunnData.kunn_id,
    status: kunnData.status,
    data: data,
    note: note,
  });
}

async function getKunnRelation(kunnId) {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const data = await sqlHelper.getData({
    table: TABLE.KUNN,
    where: {
      contract_number: kunnData.contract_number,
      partner_code: kunnData.partner_code,
    },
    order: "created_date DESC",
  });
  //remove current KUNN from relation
  const filteredData = data.filter((item) => item.kunn_id !== kunnId);
  return new SuccessResponse(filteredData);
}

async function processCallbackWaitingCicKunn({ kunnId, decision, body }) {
  try {
    const status = ["A", "B", "C"].includes(decision) ? KUNN_STATUS.PASSED_TD2 : KUNN_STATUS.REFUSED;
    const kunn = snakeToCamel(await kunnRepo.getKunnData(kunnId, true));
    const cicLog = await sqlHelper.findOne({
      table: `loan_cic_log`,
      whereCondition: {
        contract_number: kunn.kunnId,
        step: CIC_STEP_CHECK.KUNN,
      },
      orderBy: {
        created_at: "DESC",
      },
    });
    if (!cicLog?.id) {
      throw Error(`${kunnId} | processCallbackWaitingCicKunn error: loan_cic_log not found`);
    }
    //check avaible limit
    const expiredDate = moment().add(1, "days").format("YYYY-MM-DD");
    const responsePayload = JSON.parse(cicLog.response_payload ?? "{}");

    await kunnRepo.update(kunnId, {
      status: status,
      expired_date: expiredDate,
      loan_effect_time: responsePayload?.nextTimeCanRequest ?? LMS_DATE(),
    });
    await saveWorkflow(WORKFLOW_STAGE.CIC_RESULT, status, kunn.contractNumber, "system", kunn.kunnId);
    await loggingRepo.saveStepLog(kunnId, SERVICE_NAME.EXTERNAL, STEP.CALLBACK_CIC_KUNN, body);
    // const result = responsePayload?.decision === "D" ? false : true;
    //callback

    //routing
    if (status === KUNN_STATUS.PASSED_TD2) {
      const bodyRouting = {
        currentTask: KUNN_WORKFLOW.KUNN_TD2,
        partnerCode: kunn.partnerCode,
        contractNumber: kunn.contractNumber,
        kunnId: kunn.kunnId,
      };
      routing(bodyRouting);
    }
  } catch (error) {
    console.log(`[KUNN] processCallbackWaitingCicKunn error kunnId ${kunnId}, error ${error}`);
  }
}

const getKunnWithdrawalInfo = async (kunnId) => {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const [
    kunnData,
    changeRequest
  ] = await Promise.all([
    kunnRepo.getKunnData(kunnId),
    getLatestLenderChangeRequest({
      reference_code: kunnId,
      reference_type: LENDER_REFERENCE_TYPE.KUNN,
      status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT]
    })
  ]);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const changeRequestDetails = changeRequest ? await getLenderChangeRequestDetail(changeRequest.id) : [];
  const partnerCode = kunnData.partner_code;
  const kunnLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: kunnData,
    partnerCode,
    table: TABLE.KUNN,
    changeRequest,
    changeRequestDetails,
  });
  const result = await kunnLenderModel.bindingData();
  return new SuccessResponse({
    status: kunnData?.status,
    info: result,
  })
};

const getKunnBeneficiaryInfo = async (kunnId) => {
  if (!kunnId) {
    throw new BadRequestResponseV2([], "kunnId is required");
  }
  const [kunnData, disbursements, invoices, changeRequest] = await Promise.all([
    kunnRepo.getKunnData(kunnId),
    disbursementInfoRepo.findByKunnId(kunnId),
    getInvoicesWithDocumentsForBZHM(kunnId),
    getLatestLenderChangeRequest({
      reference_code: kunnId,
      reference_type: LENDER_REFERENCE_TYPE.KUNN,
      status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT]
    })
  ]);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN not found");
  }
  const changeRequestDetails = changeRequest ? await getLenderChangeRequestDetail(changeRequest.id) : [];
  const partnerCode = kunnData.partner_code;
  const disbursementInfoLenderModel = await BizziLimitKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: disbursements,
    partnerCode,
    table: "kunn_disbursement_info",
    changeRequest,
    changeRequestDetails
  });
  const disbursementsData = await disbursementInfoLenderModel.bindingData();
  const disbursementColumns = await disbursementInfoLenderModel.columnConfig;
  const invoiceLenderModel = await BizziKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: invoices,
    partnerCode,
    table: "invoice",
  });
  const invoiceColumns = await invoiceLenderModel.columnConfig;
  const invoicesDataMapping = (await invoiceLenderModel.extendData()).reduce((o, el) => {
    const disbursementInfoId = +el.debt_contract_number.split('_')[1];
    const debtContractNumber = +el.debt_contract_number.split('_')[0];
    if (!o[disbursementInfoId]) {
      o[disbursementInfoId] = [];
    }
    o[disbursementInfoId].push({
      ...el,
      debt_contract_number: debtContractNumber,
      disbursement_info_id: disbursementInfoId,
    });
    return o;
  }, {});
  const disbursementDocuments = await documentRepo.getDisbursementDocuments(kunnId);
  const documentLenderModel = await BizziLimitKunnLender.init({
    referenceNumber: kunnData.kunn_id,
    data: disbursementDocuments,
    partnerCode,
    table: "kunn_document",
  });
  const disbursementDocumentsMapping = (documentLenderModel.extendData()).reduce((o, el) => {
    if (!o[el.reference_id]) {
      o[el.reference_id] = [];
    }
    o[el.reference_id].push(el);
    return o;
  }, {});
  const data = disbursementsData.map((item) => {
    const disbursementId = item[0].key.split('/')[2];
    return {
      info: item,
      invoices: invoicesDataMapping[disbursementId] || [],
      files: disbursementDocumentsMapping[disbursementId] || [],
    };
  });
  return new SuccessResponse({ disbursements: data, disbursementColumns, invoiceColumns });
};

module.exports = {
  requestDisbursement,
  resubmitDisbursement,
  searchKunnLender,
  approveKunnLender,
  resubmitKunnLender,
  getKunnDetail,
  getKunnInvoices,
  saveKunn,
  getLoanContractByKunn,
  cancelKunnLender,
  rejectKunnChangeRequest,
  approveKunnChangeRequest,
  saveUploadDocument,
  deleteDocument,
  getRelationDocuments,
  getSubInfo,
  processSubInfo,
  getKunnDocumentSigned,
  getDataForTemplateBTTBCTD,
  processKunnSignedDocument,
  submitKunnSignedDocument,
  getKunnStatus,
  getKunnRelation,
  reSubmitKunnSignedDocument,
  processCallbackWaitingCicKunn,
  getKunnWithdrawalInfo,
  getKunnBeneficiaryInfo,
};
