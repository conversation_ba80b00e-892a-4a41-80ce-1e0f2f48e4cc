const BaseLenderModel = require("../base/base-lender-model");
const uuid = require("uuid");
const { getLenderChangeRequestDetail, updateLenderChangeRequest, getLatestLenderChangeRequest, getLatestChangeRequestsByParent, updateCancelLenderChangeRequest, createLenderChangeRequest, insertChangeRequestDetails } = require("../repositories/lender-change-request-repo");
const _ = require("lodash");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const { REF_TABLE, BIZZ_CALLBACK_TYPE, TABLE, KUNN_DOCS_BZHM } = require("../const/variables-const");
const { LENDER_REQUEST_TYPE, LENDER_REFERENCE_TYPE } = require("../const/variables-const");
const { updateKUStatusV2 } = require("../repositories/kunn-repo");
const { KUNN_STATUS } = require("../const/caseStatus");
const { SERVICE_NAME, KUNN_WORKFLOW } = require("../const/definition");
const documentRepo = require("../repositories/document");
const invoiceRepo = require("../repositories/invoice-repo");
const { routing } = require("../services/workflow-router");
const kunnRepo = require("../repositories/kunn-repo");
const s3Service = require("../upload_document/s3-service");
const callbackService = require("../services/callback-service");
const actionAuditService = require("../services/action-audit");
const { CASE_STATUS } = require("../const/code-const");

class BizziLimitKunnLender extends BaseLenderModel {
  constructor({ referenceNumber, data, partnerCode, table, url, changeRequest, changeRequestDetails }) {
    super({ referenceNumber, data, partnerCode, table, url, changeRequest, changeRequestDetails });
    this.table = table || 'kunn';
  }

  async initialize() {
    await this.getLenderColumnConfig();
    await this.bindingData();
  }

  async processFile({files, docs, kunnId, parentReference, refTable, referenceType, requestType }) {
    //create change request
    for(const file of files) {
      const doc = docs.find(doc => doc.doc_id === file.doc_id);
      const body = {
        kunnId: kunnId,
        parentReference,
        refTable,
        referenceType,
        requestType,
        info: [
          {
            key: "file_key",
            value: file.file_key,
            old_value: doc.file_key,
            status: true
          },
          {
            key: "file_name",
            value: file.file_name,
            old_value: doc.file_name,
            status: true
          }
        ]
      };
      const changeRequest = await createLenderChangeRequest({
        request_id: uuid.v4(),
        request_type: requestType,
        reference_code: doc.id,
        reference_type: referenceType,
        created_by: 'system',
        request_body: body,
        parent_reference: parentReference
      });
      const details = body.info.map(item => ({
        change_request_id: changeRequest?.id,
        key: item.key,
        old_value: item.old_value,
        new_value: item.value,
        status: item.status,
        comment: item?.comment,
        editable: true,
        is_change: true,
        ref_table: refTable
      }));

      await insertChangeRequestDetails(details);

      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
  }

  async checkFiles(files, kunnId) {
    const docs = await documentRepo.getKunnDocNotByEVFAndDocTypes(kunnId, KUNN_DOCS_BZHM);
    let resubmitFiles = [];
    let isValidAll = files.every(file => file.status === true) || files.length === 0;

    const listFileChangeKey = files.filter(file => {
      const doc = docs.find(doc => doc.doc_id === file.doc_id);
      return doc && file.file_key !== doc.file_key;
    });

    if(listFileChangeKey && listFileChangeKey.length > 0) {
      for (const file of listFileChangeKey) {
        const exist = await s3Service.checkS3FileExists(file.file_key);
        if (!exist) {
          throw new Error(`File ${file.file_key} does not exist in S3`);
        }
      }
    }

    for (const file of files) {
      const doc = docs.find(doc => doc.doc_id === file.doc_id);
      if(doc && !file.status){
        resubmitFiles.push(file);
        // update file status to resubmit
        await documentRepo.saveKunnDocumentChecked({
          kunnId: kunnId,
          docId: doc.doc_id,
          docType: doc.doc_type,
          status: file.status,
          comment: file.comment || ''
        });
      }
    }
    if(listFileChangeKey.length > 0) {
      isValidAll = false;
      await this.processFile({
        files: listFileChangeKey,
        docs: docs,
        kunnId: kunnId,
        parentReference: kunnId,
        refTable: REF_TABLE.LOAN_CONTRACT_DOCUMENT,
        referenceType: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
        requestType: LENDER_REQUEST_TYPE.LENDER_KUNN_SUB_INFO_PROCESS
      });
    }
    return {
      isValidAll: isValidAll,
      resubmitFiles: resubmitFiles,
      hasChange: listFileChangeKey.length > 0
    }
  }

  async checkInvoiceFiles(files, invoiceId) {
    const docs = await invoiceRepo.getInvoiceDocumentByInvoiceId(invoiceId);
    let resubmitFiles = [];
    let isValidAll = files.every(file => file.status === true) || files.length === 0;

    const listFileChangeKey = files.filter(file => {
      const doc = docs.find(doc => doc.doc_id === file.doc_id);
      return doc && file.file_key !== doc.file_key;
    });

    if(listFileChangeKey && listFileChangeKey.length > 0) {
      for (const file of listFileChangeKey) {
        const exist = await s3Service.checkS3FileExists(file.file_key);
        if (!exist) {
          throw new Error(`File ${file.file_key} does not exist in S3`);
        }
      }
    }

    for (const file of files) {
      const doc = docs.find(doc => doc.doc_id === file.doc_id);
      if(doc && !file.status){
        resubmitFiles.push(file);
        // update file status to resubmit
        await invoiceRepo.saveInvoiceDocumentChecked({
          invoiceId: invoiceId,
          docId: doc.doc_id,
          docType: doc.doc_type,
          status: file.status,
          comment: file.comment || ''
        });
      }
    }
    if(listFileChangeKey.length > 0) {
      isValidAll = false;
      await this.processFile({
        files: listFileChangeKey,
        docs: docs,
        parentReference: invoiceId,
        refTable: REF_TABLE.INVOICE_DOCUMENT,
        referenceType: LENDER_REFERENCE_TYPE.INVOICE_DOCUMENT,
        requestType: LENDER_REQUEST_TYPE.LENDER_KUNN_SUB_INFO_PROCESS
      });
    }
    return {
      isValidAll: isValidAll,
      resubmitFiles: resubmitFiles,
      hasChange: listFileChangeKey.length > 0
    }
  }

  async checkInvoices(invoices, kunnId) {
    const kunnInvoices = await invoiceRepo.getInvoicesByKunn(kunnId);
    let resubmitInvoices = [];
    const isValidAll = invoices.every(invoice => invoice.status === true) || invoices.length === 0;

    for (const invoice of invoices) {
      const kunnInvoice = kunnInvoices.find(inv => inv.invoice_number === invoice.invoice_number);
      if (kunnInvoice && !invoice.status) {
        resubmitInvoices.push({
          ...invoice,
          invoice_order: kunnInvoice.invoice_order,
        });
        // update invoice status to resubmit
        await invoiceRepo.updateInvoice(kunnInvoice.id, {
          waiting_resubmit: true,
          comment: invoice.comment || '',
        });
      }
    }
    return {
      isValidAll: isValidAll,
      resubmitInvoices: resubmitInvoices,
    }
  }

  async process(body) {
    const { kunnId } = body;
    body.requestType = LENDER_REQUEST_TYPE.LENDER_KUNN_PROCESS;
    body.referenceType = LENDER_REFERENCE_TYPE.KUNN;
    body.requestId = uuid.v4();
    const changeRequest = await super.process(body, REF_TABLE.KUNN);
    const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);

    const hasChange = _.some(changeRequestDetails, {editable: true, is_change: true});
    const hasResubmit = _.some(changeRequestDetails, {status: false});
    const checkFiles = await this.checkFiles(body?.files ?? [], kunnId);
    const checkInvoices = await this.checkInvoices(body?.invoices ?? [], kunnId);
    if (!hasChange && !hasResubmit && checkFiles.isValidAll && checkInvoices.isValidAll) {
      console.log(`[process] No change or resubmit, approving change request for kunnId: ${kunnId}`);
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.PASSED_MANUAL_PROCESS, kunnId);
      await updateKUStatusV2(kunnId, KUNN_STATUS.PASSED_MANUAL_PROCESS);
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.APPROVED,
        meta: JSON.stringify({
          checkFiles,
          checkInvoices
        }),
      });
      //run workflow
      const bodyRouting = {
        currentTask: KUNN_WORKFLOW.KUNN_MANUAL_REVIEW,
        partnerCode: this.data?.partner_code,
        contractNumber: this.data?.contract_number,
        kunnId: kunnId,
      }
      routing(bodyRouting);

    } else if(hasChange) {
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.WAITING_APPROVE_CHANGE_REQUEST, kunnId);
      await updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_APPROVE_CHANGE_REQUEST);
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    } else if (hasResubmit || !checkFiles.isValidAll || !checkInvoices.isValidAll) {
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.WAITING_RESUBMIT, kunnId);
      await kunnRepo.updateKUNNWaitingResubmit(kunnId);
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
      });

      //callback to resubmit
      callbackService.callbackKunnBizzi(kunnId);
    }

    //files
    if(checkFiles.hasChange) {
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.WAITING_APPROVE_CHANGE_REQUEST, kunnId);
      await updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_APPROVE_CHANGE_REQUEST);
    }

    return changeRequest;
  }

  buildCallbackPayload(changeRequestDetails, type = BIZZ_CALLBACK_TYPE.REQUEST_DISBURSE) {
    const data = {};

    for (const detail of changeRequestDetails) {
      if (detail.status === false && detail.is_change === false) {
        data[detail.key] = {
          value: detail.old_value || '',
          comment: detail.comment || ''
        };
      }
    }

    return {
      type,
      data
    };
  }

  async callback(changeRequest, resubmitFiles, resubmitInvoices) {
    const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);
    const dataCallback = this.buildCallbackPayload(changeRequestDetails);
    if (resubmitFiles && resubmitFiles.length > 0) {
      dataCallback.files = resubmitFiles;
    }
    if (resubmitInvoices && resubmitInvoices.length > 0) {
      dataCallback.invoices = resubmitInvoices;
    }
    const body = {
      requestId: uuid.v4(),
      debtContractNumber: changeRequest.reference_code,
      ...dataCallback
    };
    // callback to partner
    const partnerUrl = '';
    const response = '';

    this.stepLogging({
      serviceName: SERVICE_NAME.KUNN_CALLBACK,
      step: KUNN_WORKFLOW.CALLBACK,
      body,
      response: response,
      url: partnerUrl
    });
    return body;
  }

  async processSubInfo(body) {
    const { subType, referenceId } = body;
    body.requestType = LENDER_REQUEST_TYPE.LENDER_KUNN_SUB_INFO_PROCESS;
    body.referenceType = subType;
    body.requestId = uuid.v4();
    body.parentReference = body?.kunnId;
    const changeRequest = await super.process(body, subType);
    if(changeRequest?.id) {
      await updateCancelLenderChangeRequest({
        request_type: body.requestType,
        reference_code: body.referenceId,
        reference_type: body.referenceType,
        status: [LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT]
      });
    }
    const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);

    const checkFiles = await this.checkInvoiceFiles(body?.files ?? [], referenceId);
    const hasChange = _.some(changeRequestDetails, {editable: true, is_change: true});
    const hasResubmit = _.some(changeRequestDetails, {status: false});
    if (!hasChange && !hasResubmit && checkFiles.isValidAll) {
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.APPROVED,
      });
    } else if(hasChange) {
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    } else if (hasResubmit || !checkFiles.isValidAll) {
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
      });
    }
    return changeRequest;
  }

  async approveSubInfo(parentId) {
    const listChangeRequest = await getLatestChangeRequestsByParent(LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE, parentId);
    if (!listChangeRequest || listChangeRequest.length === 0) {
      console.log(`[BizziKunnLender] No change request of sub-info found for parentId: ${parentId}`);
      return;
    }
    for (const changeRequest of listChangeRequest) {
      const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);
      const hasChange = _.some(changeRequestDetails, {editable: true, is_change: true});
      const hasResubmit = _.some(changeRequestDetails, {status: false});
      if (hasChange) {
        const listChange = changeRequestDetails.filter(item => item.is_change && item.editable);
        if (listChange && listChange.length > 0) {
          let updateData = {};
          for (const item of listChange) {
            updateData[item.key] = item.new_value;
          }
          if(changeRequest.reference_type === LENDER_REFERENCE_TYPE.INVOICE) {
            await invoiceRepo.updateInvoice(changeRequest.reference_code, updateData);
            //process invoice_document
            await this.approveSubInfo(changeRequest.id);
          }
          if(changeRequest.reference_type === LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT) {
            await documentRepo.update(changeRequest.reference_code, updateData);
          }

          if(changeRequest.reference_type === LENDER_REFERENCE_TYPE.INVOICE_DOCUMENT) {
            await invoiceRepo.updateDocument(changeRequest.reference_code, updateData);
          }
        }
      }
      const lenderStatus = hasResubmit ? LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT : LENDER_CHANGE_REQUEST_STATUS.APPROVED;
      await updateLenderChangeRequest(changeRequest.id, {
        status: lenderStatus,
      });

      //update resubmit if change request is invoice
      if(changeRequest.reference_type === LENDER_REFERENCE_TYPE.INVOICE && hasResubmit) {
        const invoiceUpdateData = {
          waiting_resubmit: true
        }
        await invoiceRepo.updateInvoice(changeRequest.reference_code, invoiceUpdateData);
      }
    }
  }

  async checkSubInfoHasChangeOrResubmit(status, kunnId) {
    const listChangeRequest = await getLatestChangeRequestsByParent(status, kunnId);
    if (!listChangeRequest || listChangeRequest.length === 0) {
      console.log(`[BizziKunnLender] No change request of sub-info found for kunnId: ${kunnId}`);
      return {
        hasChange: false,
        hasResubmit: false,
      };
    }
    for (const changeRequest of listChangeRequest) {
      const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);
      const hasResubmit = _.some(changeRequestDetails, {status: false});
      const hasChange = _.some(changeRequestDetails, {editable: true, is_change: true});
      if (hasResubmit || hasChange) {
        return {
          hasChange: hasChange,
          hasResubmit: hasResubmit,
        };
      }
    }
    return {
      hasChange: false,
      hasResubmit: false,
    };
  }

  async approve(body) {
    const { comment, kunnId } = body;
    const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE });
    //override key value
    const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest?.id);
    const hasChange = _.some(changeRequestDetails, {editable: true, is_change: true});
    const hasResubmit = _.some(changeRequestDetails, {status: false});
    if(hasChange) {
      const listChange = await changeRequestDetails.filter(item => item.is_change && item.editable);
      if( listChange && listChange.length > 0) {
        const updateData = {};
        for (const item of listChange) {
          updateData[item.key] = item.new_value;
        }
        await kunnRepo.update(kunnId, updateData);
      }
    }
    await this.approveSubInfo(kunnId);
    //callback if need
    await updateLenderChangeRequest(changeRequest?.id, {
      status: LENDER_CHANGE_REQUEST_STATUS.APPROVED,
      comment: comment || ''
    });

    const subInfoChangeOrSubmit = await this.checkSubInfoHasChangeOrResubmit(LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT, kunnId);
    if(!hasResubmit && !subInfoChangeOrSubmit.hasResubmit) {
      //same as approved
      await updateKUStatusV2(kunnId, KUNN_STATUS.APPROVED_CHANGE_REQUEST);
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.APPROVED_CHANGE_REQUEST, kunnId);
      //run workflow
      const bodyRouting = {
        currentTask: KUNN_WORKFLOW.KUNN_MANUAL_REVIEW,
        partnerCode: this.data?.partner_code,
        contractNumber: this.data?.contract_number,
        kunnId: kunnId,
      };
      routing(bodyRouting);
    } else if(hasResubmit || subInfoChangeOrSubmit.hasResubmit) {
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.KUNN_MANUAL_REVIEW.STEP_CODE, CASE_STATUS.KUNN_MANUAL_REVIEW.ACTION.WAITING_RESUBMIT, kunnId);
      await kunnRepo.updateKUNNWaitingResubmit(kunnId);
      callbackService.callbackKunnBizzi(kunnId);
    }
  }

  async bindingData(group = null) {
    const data = this.data;
    const lenderColumnConfig = this.columnConfig;
    let result = [];
    if (Array.isArray(lenderColumnConfig)) {
      // Handle array of objects
      if (Array.isArray(data)) {
        for (const dataItem of data) {
          const itemResult = [];
          for (const col of lenderColumnConfig) {
            if (group && col.group !== group) {
              continue;
            }
            // Guarantee that key is unique
            const key = `${col.table_name}/${col.key}/${dataItem.id}`;
            const value = dataItem.hasOwnProperty(key) ? dataItem[key] : null;
            const extendData = this.getStatusAndLatestComment(key, value);
            itemResult.push({
              ...col,
              value: value,
              ...this.defaultExtend(extendData),
              key
            });
          }
          result.push(itemResult);
        }
      } else {
        // Handle single object (original behavior)
        for (const col of lenderColumnConfig) {
          if (group && col.group !== group) {
            continue;
          }
          const key = col.key;
          const value = data.hasOwnProperty(key) ? data[key] : null;
          const extendData = this.getStatusAndLatestComment(key, value);
          result.push({
            ...col,
            value: value,
            ...this.defaultExtend(extendData),
          });
        }
      }
    }

    this.bindedData = result;
    return result;
  }
}

module.exports = BizziLimitKunnLender;