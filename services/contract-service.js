const loanContractRepo = require("../repositories/loan-contract-repo");
const dateHelper = require("../utils/dateHelper");
const VNnum2words = require("vn-num2words");
const { getValueCode_v3, getPlace } = require("../utils/masterdataService");
const utils = require("../utils/helper");
const {
  SERVICE_NAME,
  FILE_STORAGE,
  PARTNER_CODE,
  CONTRACT_TYPE,
  KUNN_WORKFLOW,
  BTT_CREDITLIMIT_WORKFLOW,
  DOC_TYPE,
  DATE_FORMAT
} = require("../const/definition");
const s3Service = require("../upload_document/s3-service");
const esingingRepo = require("../repositories/loan-esigning");
const callbackService = require("../services/callback-service");
const smsService = require("../utils/smsService");
const common = require("../utils/common");
const fs = require("fs");
const PizZip = require("pizzip");
const libre = require("libreoffice-convert");
const uuid = require("uuid");
const docxtemplater = require("docxtemplater");
const moment = require("moment-timezone");
moment().tz("Asia/Ho_Chi_Minh").format();
const { STATUS, CALLBACK_STAUS, KUNN_STATUS } = require("../const/caseStatus");
const offerRepo = require("../repositories/offer");
const { numberToString } = require("../utils/numberToString");
const { serviceEndpoint } = require("../const/config");
const { sendNotification } = require("./notification-service");
const {
  TASK_NAME,
  KUNN_TEMPLATE_DOCTYPE,
  AF3_TEMPLATE_DOCTYPE,
  AF3_TEMPLATE_DOCTYPE_SIGNED,
  FINV_KUNN_DOCTYPES,
  KUNN_TEMPLATE_DOCTYPES_AND_PARTNER_MAPPING,
  FINV_DOCTYPES,
  ADDRESS_CODE_TYPE,
  MASTER_DATA
} = require("../const/variables-const");
const { generateFileApi, addTaskConvertFile } = require("../apis/convert-service.api");
const kunnRepo = require("../repositories/kunn-repo");
const { getDataForTemplateBTTKUNN, getDataForTemplateBTTXNCN, getDataForTemplateBTTTTCN, getDataForTemplateBTTTTRV, checkAllKunnDocumentsExist, getDataForTemplateLCTKU, getDataFinVForTemplateTTRV } = require("./kunn-service");
const { getDataForTemplateBTTHDTD, getDataForTemplateBTTCNKPT, getDataForTemplateBTTKCBB, getDataForTemplateBTTQDCV } = require("./creditlimit-service");
const { saveStepLog } = require("../repositories/logging-repo");
const { getDataForTemplateBTTBCTD } = require("../KUNN/bizzi-kunn");
const sqlHelper = require("../utils/sqlHelper");
const loggingRepo = require("../repositories/logging-repo");
const crmHelper = require("../utils/crmService");
const { generateDocumentNo } = require("../utils/helper");
const loanCustomerRepo = require("../repositories/loan-customer-repo");
const { getText } = require("number-to-text-vietnamese");
const actionAuditService = require("./action-audit");
const { CASE_STATUS } = require("../const/code-const")
const { formatDate } = require("../utils/dateHelper");

async function generateContract(contractNumber, partnerCode, isSelectOffer = false) {
  try {
    if (partnerCode === PARTNER_CODE.FINV) {
      return await generateFinVDoc(contractNumber);
    }

    let fullFillData;
    const contractData = await loanContractRepo.getLoanContract(contractNumber);
    const rootContractNumber = contractData?.root_contract_number;
    let templateFilePath = "./static_file/HDTD_TEMPLATE_MERCHANT.docx";
    if (!utils.isNullOrEmpty(rootContractNumber) && partnerCode !== PARTNER_CODE.MISA) {
      fullFillData = await getContractData(contractNumber, rootContractNumber);
      templateFilePath = "./static_file/phu_luc_review_hm.docx";
    } else if (partnerCode == PARTNER_CODE.VTP || (partnerCode == PARTNER_CODE.VSK && contractData.contract_type == CONTRACT_TYPE.CASH_LOAN)) {
      fullFillData = await getContractData(contractNumber);
      templateFilePath = "./static_file/HDTD_TEMPLATE_MERCHANT_NEW_FEE.docx";
    } else if ([PARTNER_CODE.VSK, PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode) && contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
      fullFillData = await getContractDataHM(contractNumber);
      templateFilePath = "./static_file/hanmuc_contract.docx";
      if (partnerCode == PARTNER_CODE.SMA) templateFilePath = "./static_file/hanmuc_contract_sma.docx";
    } else if (partnerCode == PARTNER_CODE.MISA && contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
      fullFillData = await getContractDataHmSME(contractNumber);
      templateFilePath = "./static_file/hanmuc_sme_contract.docx";
    } else if (partnerCode === PARTNER_CODE.MISA && contractData.contract_type === CONTRACT_TYPE.CASH_LOAN) {
      fullFillData = await getContractDataHmSME(contractNumber);
      templateFilePath = "./static_file/vaymon_sme_contract.docx";
    } else {
      fullFillData = await getContractData(contractNumber);
    }
    await convert2push(templateFilePath, fullFillData, FILE_STORAGE.storageContractUnSignedPath, global.config, contractNumber).then(async (unsignedData) => {
      const saveUnsignedRs = await esingingRepo.saveUnsignedContract(contractNumber, unsignedData.Location, unsignedData.Key);
      if (!saveUnsignedRs) {
        return false;
      } else {
        await loanContractRepo.updateContractStatus(STATUS.WAITING_TO_BE_SIGNED, contractNumber);
        if (partnerCode == PARTNER_CODE.VPL || partnerCode === PARTNER_CODE.MISA) {
          callbackService.callbackPartner(contractNumber, contractData.partner_code, CALLBACK_STAUS.APPROVED);
          if (partnerCode === PARTNER_CODE.MISA) {
            const isEnable = global.config.data.smsService.useSMS;
            const smsUrl = global.config.data.smsService.sendSMS;
            let msg = global.config.data.smsService.esigningSmeHmMsg;
            msg = msg.replace("contractNumber", contractNumber);
            const phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
            try {
              if (phoneNumberRs !== undefined) {
                if (isEnable) {
                  await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
                }
              }
            } catch (error) {
              console.log(error);
            }
          }
        }
        if ([PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
          if (!isSelectOffer) callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.SIGNED);
          // try {

          //     const body = {
          //         phoneNumber: contractData?.phone_number1,
          //         title: 'Hạn mức đã được phê duyệt, mời bạn vào ký',
          //         message: `Mời bạn vào ký hợp đồng hạn mức ${contractNumber}`,
          //         value: {
          //             contractNumber: contractNumber
          //         }
          //     };
          //     const endPoint = serviceEndpoint.NOTIFICATION.LIMIT_ACTIVE;
          //     sendNotification( body, endPoint, global.config );
          // } catch (err) {
          //     common.log('send notification to app mc error', contractNumber)
          //     console.log(err?.message)
          // }
        }
        if (![PARTNER_CODE.MISA].includes(partnerCode)) {
          smsService.sendEsign(contractNumber, contractData.phone_number1, contractData.contract_type);
        }
        return true;
      }
    });
  } catch (err) {
    console.log(err);
    common.log(`generate contract error : ${err.message}`, contractNumber);
    return false;
  }
}

async function getContractData(contractNumber, rootContractNumber = undefined) {
  const contractData = await loanContractRepo.getLoanContract(contractNumber);
  let contractRootData;
  if (rootContractNumber !== undefined) {
    contractRootData = await loanContractRepo.getLoanContract(rootContractNumber);
  }
  if (!contractData) {
    return false;
  }

  const masterData = await Promise.all([
    rootContractNumber === undefined ? getValueCode_v3(contractData.id_issue_place, "ISSUE_PLACE_VN") : getValueCode_v3(contractRootData?.id_issue_place, "ISSUE_PLACE_VN"),
    getValueCode_v3(contractData.ward_cur, "WARD"),
    getValueCode_v3(contractData.district_cur, "DISTRICT"),
    getValueCode_v3(contractData.province_cur, "PROVINCE"),
    getValueCode_v3(contractData.ward_per, "WARD"),
    rootContractNumber === undefined ? getValueCode_v3(contractData.district_per, "DISTRICT") : getValueCode_v3(contractRootData?.district_per, "DISTRICT"),
    rootContractNumber === undefined ? getValueCode_v3(contractData.province_per, "PROVINCE") : getValueCode_v3(contractRootData?.province_per, "PROVINCE"),
    getValueCode_v3(contractData.loan_purpose, "LOAN_PURPOSE"),
    getValueCode_v3(contractData.bank_code, "BANK"),
    getValueCode_v3(contractRootData?.other_issue_place, "ISSUE_PLACE_VN"),
  ]);

  let loan_amount = parseFloat(contractData.approval_amt);
  loan_amount = isNaN(loan_amount) ? 0 : loan_amount;

  let date_of_birth = dateHelper.formatDate(contractData.birth_date, "DD/MM/YYYY");
  let issue_date = dateHelper.formatDate(contractData.id_issue_dt, "DD/MM/YYYY");
  let issue_date_oth = !utils.isNullOrEmpty(contractRootData?.other_issue_date) ? dateHelper.formatDate(contractRootData?.other_issue_date, "DD/MM/YYYY") : "";
  let loan_amount_text = VNnum2words(loan_amount).trim();
  loan_amount_text = loan_amount_text.charAt(0).toUpperCase() + loan_amount_text.slice(1);

  let ir_rate_year = parseInt(contractData.approval_int_rate * 100);
  let ir_rate_month = parseFloat(ir_rate_year / 12);
  ir_rate_month = ir_rate_month.toFixed(2);
  ir_rate_year = ir_rate_year.toFixed(2);

  let cur_address = `${contractRootData?.address_cur}, ${masterData[1]}, ${masterData[2]}, ${masterData[3]}`;
  let per_address = `${contractRootData?.address_per}, ${masterData[4]}, ${masterData[5]}, ${masterData[6]}`;
  const dataFullFill = {
    contract_number: contractNumber,
    ngay: dateHelper.getDate(),
    thang: dateHelper.getMonth(),
    nam: dateHelper.getYear(),
    customer_name: contractData.cust_full_name,
    date_of_birth: date_of_birth,
    identity_card: contractData.id_number,
    issue_date: issue_date,
    issue_place: masterData[0],
    tem_detail_address: contractData.address_cur,
    tem_ward: masterData[1],
    tem_district: masterData[2],
    tem_province: masterData[3],
    permanent_detail_address: contractData.address_per,
    permanent_ward: masterData[4],
    permanent_district: masterData[5],
    permanent_province: masterData[6],
    phone_number: contractData.phone_number1,
    email: contractData.email || "",
    loan_purpose: masterData[7],
    loan_amount: utils.convertNumber(loan_amount),
    loan_amount_text: loan_amount_text,
    tenor: contractData.approval_tenor,
    ir_rate_month: ir_rate_month,
    ir_rate_year: ir_rate_year,
    account_number: contractData.bank_account,
    bank_name: masterData[8],
    bank_branch_name: "",
    beneficiary_name: contractData.bank_account_owner || "",
    identity_card_oth: contractRootData?.other_id_number || "",
    issue_date_oth: issue_date_oth || "",
    issue_place_oth: masterData[9] || "",
    cur_address,
    per_address,
    contract_number_old: rootContractNumber,
    contract_approval_date: moment(contractRootData?.approval_date).format("DD/MM/YYYY") || "",
  };
  // console.log({dataFullFill})
  return dataFullFill;
}

async function getContractDataHM(contractNumber) {
  const contractData = await loanContractRepo.getLoanContract(contractNumber);
  if (!contractData) {
    return false;
  }

  const masterData = await Promise.all([getValueCode_v3(contractData.id_issue_place, "ISSUE_PLACE_VN"), getValueCode_v3(contractData.ward_cur, "WARD"), getValueCode_v3(contractData.district_cur, "DISTRICT"), getValueCode_v3(contractData.province_cur, "PROVINCE"), getValueCode_v3(contractData.ward_per, "WARD"), getValueCode_v3(contractData.district_per, "DISTRICT"), getValueCode_v3(contractData.province_per, "PROVINCE"), getValueCode_v3(contractData.loan_purpose, "LOAN_PURPOSE"), getValueCode_v3(contractData.bank_code, "BANK"), offerRepo.getSelectedOffer(contractNumber)]);

  // let loan_amount = parseFloat(contractData.approval_amt);
  // loan_amount = isNaN(loan_amount) ? 0 : loan_amount;

  // let date_of_birth = dateHelper.formatDate(contractData.birth_date, "DD/MM/YYYY");
  // let issue_date = dateHelper.formatDate(contractData.id_issue_dt, "DD/MM/YYYY");
  // let loan_amount_text = VNnum2words(loan_amount).trim();
  // loan_amount_text = loan_amount_text.charAt(0).toUpperCase() + loan_amount_text.slice(1);

  // let ir_rate_year = parseInt(contractData.approval_int_rate * 100);
  // let ir_rate_month = parseFloat(ir_rate_year / 12);
  // ir_rate_month = ir_rate_month.toFixed(2);
  // ir_rate_year = ir_rate_year.toFixed(2);
  let duration = 36;
  if (contractData?.partner_code === PARTNER_CODE.MCAPP) duration = contractData?.approval_tenor || 12;
  const currentDate = new Date();
  const dataFullFill = {
    date: dateHelper.getDate(),
    month: dateHelper.getMonth(),
    year: dateHelper.getYear(),
    contract_number: contractData.contract_number || "",
    duration: duration,
    contract_approval_date: moment(currentDate).format("DD-MM-YYYY") || "",
    customer_name: contractData.cust_full_name || "",
    date_of_birth: moment(contractData.birth_date).format("DD-MM-YYYY") || "",
    identify_id: contractData.id_number || "",
    identify_date: moment(contractData.id_issue_dt).format("DD-MM-YYYY") || "",
    identify_address: masterData[0],
    current_address: contractData.address_cur || "",
    current_ward: masterData[1] || "",
    current_district: masterData[2] || "",
    current_province: masterData[3] || "",
    per_address: contractData.address_per || "",
    per_ward: masterData[4] || "",
    per_district: masterData[5] || "",
    per_province: masterData[6] || "",
    phone_number: contractData.phone_number1 || "",
    email: contractData.email || "",
    approval_amount: utils.numberWithCommas(parseInt(masterData[9].offer_amt)) || "",
    approval_amount_string: numberToString(+masterData[9].offer_amt),
  };
  // console.log({dataFullFill})
  return dataFullFill;
}

async function getContractDataHmSME(contractNumber) {
  const contractData = await loanContractRepo.getLoanContract(contractNumber);
  if (!contractData) {
    return false;
  }

  const masterData = await Promise.all([getValueCode_v3(contractData.sme_headquarters_ward, "WARD"), getValueCode_v3(contractData.sme_headquarters_district, "DISTRICT"), getValueCode_v3(contractData.sme_headquarters_province, "PROVINCE"), getValueCode_v3(contractData.bank_code, "BANK"), getValueCode_v3(contractData.bank_name2, "BANK"), getValueCode_v3(contractData.sme_representation_position, "PROFESSION"), getValueCode_v3(contractData.authorized_position, "PROFESSION"), offerRepo.getSelectedOffer(contractNumber)]);

  let fullAddress = contractData.sme_headquarters_address + ", " + masterData[0] + ", " + masterData[1] + ", " + masterData[2];
  let cusName = utils.isNullOrEmpty(contractData.authorized_name) ? contractData.sme_representation_name : contractData.authorized_name;
  let position = utils.isNullOrEmpty(contractData.authorized_position) ? masterData[5] : masterData[6];
  const dataFullFill = {
    dd: dateHelper.getDate(),
    mm: dateHelper.getMonth(),
    yy: dateHelper.getYear(),
    contract_number: contractData.contract_number || "",
    regist_number: contractData.registration_number || "",
    regist_date: moment(contractData.first_registration_date).format("DD-MM-YYYY") || "",
    sme_name: contractData.sme_name || "",
    sme_name_upper: contractData.sme_name.toUpperCase() || "",
    sme_address: fullAddress || "",
    sme_phone: contractData.sme_phone_number || "",
    bank_number: contractData.bank_account || "",
    bank_name: masterData[3] || "",
    bank_number2: contractData.bank_account2 || "",
    bank_name2: masterData[4] || "",
    sme_representation_name: cusName || "",
    position: position || "",
    author_number: contractData.authorization_letter_number || "",
    author_date: moment(contractData.authorization_letter_singed_date).format("DD-MM-YYYY") || "",
    customer_name: cusName.toUpperCase() || "",
    approval_amount: utils.numberWithCommas(parseInt(masterData[7].offer_amt)) || "",
    approval_amount_string: numberToString(+masterData[7].offer_amt),
    rate: contractData.approval_int_rate * 100 || "",
    tenor: contractData.approval_tenor || "",
  };
  return dataFullFill;
}

function fillData2Template(filePath, contractData) {
  try {
    let contractTemplatePath = filePath;
    let fileBuffer = fs.readFileSync(contractTemplatePath, "binary");
    let zip = new PizZip(fileBuffer);
    let doc = new docxtemplater(zip);
    doc.setData(contractData);
    doc.render();
    let buf = doc.getZip().generate({ type: "nodebuffer" });
    let fileId = uuid.v4();
    let saveFilePath = "./static_file/" + fileId + ".docx";
    fs.writeFileSync(saveFilePath, buf);
    return saveFilePath;
  } catch (error) {
    console.log(error);
    common.log(error.message);
  }
}

function convert2push(filePath, contractData, prefix, config, contractNumber) {
  return new Promise(function (resolve, reject) {
    let tempFilePath = fillData2Template(filePath, contractData);
    const file = fs.readFileSync(tempFilePath);

    libre.convert(file, ".pdf", undefined, async (err, done) => {
      if (err) {
        common.log("Error converting file:" + err.message);
        console.log(err);

        reject(err);
      } else {
        let fileName = contractNumber + ".pdf";
        try {
          let data = await s3Service.upload(config.data, fileName, done, prefix)
          fs.unlink(tempFilePath);
          resolve(data);
        } catch (error) {
          common.log("upload s3 error: " + error.message);
          console.log(error);
          reject(error);
        }
      }
    });
  });
}

async function generateKunnContractFile(kunnId) {
  try {
    console.log('generateKunnContractFile');
    const kunnData = await kunnRepo.getKunnData(kunnId);
    if (!kunnData) {
      throw new Error(`KUNN data not found for ID: ${kunnId}`);
    }
    const partnerCode = kunnData.partner_code;
    if (partnerCode === PARTNER_CODE.BIZZ && kunnData.status === KUNN_STATUS.PASSED_TD2) {
      const listDocType = KUNN_TEMPLATE_DOCTYPE;
      for (const docType of listDocType) {
        await genBizziKunnFile(kunnId, docType);
      }
      actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.WAITING_TO_BE_SIGNED, kunnId);
    } else if (partnerCode === PARTNER_CODE.FINV) {
      for (const docType of FINV_KUNN_DOCTYPES) {
        await genFinVKunnFile(kunnId, docType);
      }
      return false;
    }
    return true;
  } catch (error) {
    common.log(`[generateKunnContractFile] error: ${error.message}`, kunnId);
    console.error(error);
    return false;
  }
}

async function generateAF3ContractFile(contractNumber) {
  try {
    // const { config } = global;
    let callback_uri = "/los-mc-credit/ui/lender/v1/af3/handler-documents-generated";
    // const callback_url = `${config?.basic.losMcCredit[[config.env]]}${path}`;

    const contractData = await loanContractRepo.getAllContractData(contractNumber);
    if (!contractData) {
      throw new Error(`contractData data not found for ID: ${contractNumber}`);
    }
    const partnerCode = contractData?.partner_code;
    if (partnerCode === PARTNER_CODE.BIZZ) {
      const listDocType = AF3_TEMPLATE_DOCTYPE;
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.WAITING_TO_BE_SIGNED, contractNumber);
      await Promise.all(listDocType.map((docType) => genBizziAF3File(contractNumber, docType, callback_uri)));
    }
    if (partnerCode === PARTNER_CODE.BZHM) {
      const listDocType = AF3_TEMPLATE_DOCTYPE;
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.WAITING_TO_BE_SIGNED, contractNumber);
      await Promise.all(listDocType.map((docType) => genBizziAF3File(contractNumber, docType, callback_uri)));
    }
    return true;
  } catch (error) {
    common.log(`[generateAF3ContractFile] error: ${error.message}`, kunnId);
    console.error(error);
    return false;
  }
}

function generateKunnDocumentNo() {
  const documentNos = new Set();
  while (documentNos.size < 4) {
    const docNo = generateDocumentNo();
    if (!documentNos.has(docNo)) {
      documentNos.add(docNo);
    }
  }
  const [BTTKUNN, BTTXNCN, BTTTTCN, BTTTTRV] = Array.from(documentNos);
  return {
    BTTKUNN,
    BTTXNCN,
    BTTTTCN,
    BTTTTRV,
  };
}

async function genBizziKunnFile(kunnId, docType) {
  try {
    let data = {};
    const documentNos = generateKunnDocumentNo();
    switch (docType) {
      case "BTTKUNN":
        data = await getDataForTemplateBTTKUNN(kunnId, documentNos);
        break;
      case "BTTXNCN":
        data = await getDataForTemplateBTTXNCN(kunnId, documentNos);
        break;
      case "BTTTTCN":
        data = await getDataForTemplateBTTTTCN(kunnId, documentNos);
        break;
      case "BTTTTRV":
        data = await getDataForTemplateBTTTTRV(kunnId, documentNos);
        break;
      default:
        throw new Error(`Unsupported docType: ${docType}`);
    }
    const rs = await addTaskConvertFile(data);
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_GENERATE_TEMPLATE, docType, data, rs);
    console.log(`Generated file for KUNN ID ${kunnId} docType ${docType}: ${JSON.stringify(rs)}`);
  } catch (err) {
    common.log(`genBizziKunnFile error: ${err.message}`, kunnId);
    console.error(err);
  }
  return true;
}

async function genBizziAF3File(contract_number, docType, callback_url) {
  try {
    let data = {};
    switch (docType) {
      case "BTTHDTD":
        data = await getDataForTemplateBTTHDTD(contract_number, callback_url);
        break;
      case "BTTCNKPT":
        data = await getDataForTemplateBTTCNKPT(contract_number, callback_url);
        break;
      case "BTTKCBB":
        data = await getDataForTemplateBTTKCBB(contract_number, callback_url);
        break;
      case "BTTQDCV":
        data = await getDataForTemplateBTTQDCV(contract_number, callback_url);
        break;
      case "BTTBCTD":
        data = await getDataForTemplateBTTBCTD(contract_number);
        break;
      default:
        throw new Error(`Unsupported docType: ${docType}`);
    }

    const rs = await addTaskConvertFile(data);
    await saveStepLog(contract_number, SERVICE_NAME.CONVERT_SERVICE, docType, data, rs);
    console.log(`Generated file for AF3 ID ${contract_number} docType ${docType}: ${JSON.stringify(rs)}`);
  } catch (err) {
    common.log(`genBizziAF3File error: ${err.message}`, contract_number);
    console.error(err);
  }
  return true;
}

async function checkGenerateKunnTemplateSuccess(kunnId) {
  const maxRetries = 5;
  const delayTime = 30000; // 30 seconds
  let attempts = 1;
  let isSuccess = false;
  while (attempts <= maxRetries && !isSuccess) {
    const completed = await checkAllKunnDocumentsExist(kunnId, KUNN_TEMPLATE_DOCTYPE);
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_CHECK_TEMPLATE_EXIST, KUNN_WORKFLOW.KUNN_CHECK_TEMPLATE_EXIST, { kunnId }, { attempts, isSuccess });

    if (completed) {
      isSuccess = true;
    } else {
      attempts++;
      await utils.delay(delayTime);
    }
  }
  if (isSuccess) {
    await kunnRepo.update(kunnId, { status: KUNN_STATUS.SIGNING_IN_PROGRESS, approve_signing_date: new Date() });
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_CHECK_TEMPLATE_EXIST, KUNN_WORKFLOW.KUNN_CHECK_TEMPLATE_EXIST, { kunnId }, { attempts, isSuccess });
    //callback to partner
    callbackService.callbackKunnBizzi(kunnId);
  }
  return isSuccess;
}

async function checkGenerateKunnTemplateSuccessV2(kunnId, partnerCode) {
  const docTypes = KUNN_TEMPLATE_DOCTYPES_AND_PARTNER_MAPPING[partnerCode];
  const maxRetries = 5;
  const delayTime = 30000; // 30 seconds
  let attempts = 1;
  let isSuccess = false;
  while (attempts <= maxRetries && !isSuccess) {
    const completed = await checkAllKunnDocumentsExist(kunnId, docTypes);
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_CHECK_TEMPLATE_EXIST, KUNN_WORKFLOW.KUNN_CHECK_TEMPLATE_EXIST, { kunnId }, { attempts, isSuccess });

    if (completed) {
      isSuccess = true;
    } else {
      attempts++;
      await utils.delay(delayTime);
    }
  }
  if (isSuccess) {
    await kunnRepo.update(kunnId, { status: KUNN_STATUS.SIGNING_IN_PROGRESS, approve_signing_date: new Date() });
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_CHECK_TEMPLATE_EXIST, KUNN_WORKFLOW.KUNN_CHECK_TEMPLATE_EXIST, { kunnId }, { attempts, isSuccess });
    // TODO: callback to partner
    // callbackService.callbackKunnBizzi(kunnId);
  }
  return isSuccess;
}

const cancelContract = async ({ contractNumber, comment, cancelledBy, loanContract, stage }) => {
  try {
    if (!loanContract?.id) {
      loanContract = await loanContractRepo.getLoanContract(contractNumber);
    }
    const nonCancelableStatuses = [STATUS.CANCELLED, STATUS.SIGNED, STATUS.WAITING_TO_BE_SIGNED, STATUS.ACTIVATED, STATUS.WAITING_EVF_SIGNATURE];

    if (nonCancelableStatuses.includes(loanContract.status)) {
      console.log(`Contract ${contractNumber} cannot be cancelled, current status: ${loanContract.status}`);
      return false;
    }

    const loanUpdated = await sqlHelper.patchUpdate({
      table: "loan_contract",
      columns: ["status", "rejection_reason", "cancelled_by", "cancelled_at"],
      values: sqlHelper.generateValues(
        {
          status: STATUS.CANCELLED,
          rejection_reason: comment,
          cancelled_by: cancelledBy,
          cancelled_at: new Date(),
        },
        ["status", "rejection_reason", "cancelled_by", "cancelled_at"]
      ),
      conditions: {
        contract_number: contractNumber,
      },
    });

    if (loanUpdated?.status !== STATUS.CANCELLED) {
      console.log(`Failed to update contract ${contractNumber} status to CANCELLED`);
      return false;
    }

    await loggingRepo.saveWorkflow(stage ?? "CREDIT-LIMIT", STATUS.CANCELLED, contractNumber, cancelledBy);
    await callbackService.callbackPartner(contractNumber, loanContract.partner_code, CALLBACK_STAUS.CANCELLED);
    crmHelper.removeContract(global.config, contractNumber);
    return true;
  } catch (e) {
    console.error(`Error cancelling contract ${contractNumber}:`, e);
    return false;
  }
};

/**
 * Job function to generate Bizzi templates for contracts created the previous day.
 * This function should be scheduled to run daily.
 */
async function generateBizziCreditLimitTemplateJobNextDay() {
  try {
    // Get yesterday's date in YYYY-MM-DD format
    const yesterday = moment().subtract(1, "days").format("YYYY-MM-DD");
    const contracts = await sqlHelper.getData({
      table: "loan_contract",
      where: {
        partner_code: PARTNER_CODE.BIZZ,
        status: STATUS.SIGNING_IN_PROGRESS,
        approval_date: {
          type: "date",
          value: yesterday,
        },
        is_delete: 0,
      },
    });
    if (!contracts || contracts.length === 0) {
      console.log(`[generateBizziCreditLimitTemplateJobNextDay] No contracts found for ${yesterday}`);
      return true;
    }
    const bodyLog = contracts.map((contract) => ({
      contract_number: contract.contract_number,
      status: contract.status,
      approval_date: contract.approval_date,
    }));
    console.log(`[generateBizziCreditLimitTemplateJobNextDay] Contracts found for ${yesterday}:`, JSON.stringify(bodyLog));
    for (const contract of contracts) {
      try {
        const listDocType = AF3_TEMPLATE_DOCTYPE;
        await sqlHelper.updateDataAny({
          table: "loan_contract_document",
          columns: ["is_deleted", "updated_date"],
          values: [1, new Date()],
          conditions: {
            contract_number: contract.contract_number,
            kunn_contract_number: null,
            is_deleted: 0,
            doc_type: listDocType,
          },
        });
        await Promise.all(listDocType.map((docType) => genBizziAF3File(contract.contract_number, docType)));
      } catch (err) {
        common.log(`[generateBizziTemplateJobNextDay] Error generating template for contract ${contract.contract_number}: ${err.message}`);
      }
    }
    return true;
  } catch (err) {
    common.log(`[generateBizziTemplateJobNextDay] Job error: ${err.message}`);
    return false;
  }
}

/**
 * Job function to generate Bizzi KUNN templates for KUNN records created the previous day.
 * This function should be scheduled to run daily.
 */
async function generateBizziKunnTemplateJobNextDay() {
  try {
    // Get yesterday's date in YYYY-MM-DD format
    const yesterday = moment().subtract(1, "days").format("YYYY-MM-DD");
    const kunnList = await sqlHelper.getData({
      table: "kunn",
      where: {
        partner_code: PARTNER_CODE.BIZZ,
        status: KUNN_STATUS.SIGNING_IN_PROGRESS,
        approve_signing_date: {
          type: "date",
          value: yesterday,
        },
      },
    });
    if (!kunnList || kunnList.length === 0) {
      console.log(`[generateBizziKunnTemplateJobNextDay] No KUNN records found for ${yesterday}`);
      return true;
    }
    const bodyLog = kunnList.map((kunn) => ({
      kunn_id: kunn.kunn_id,
      status: kunn.status,
      approve_signing_date: kunn.approve_signing_date,
    }));
    console.log(`[generateBizziKunnTemplateJobNextDay] KUNN records found for ${yesterday}:`, JSON.stringify(bodyLog));
    for (const kunn of kunnList) {
      try {
        const listDocType = KUNN_TEMPLATE_DOCTYPE;
        await sqlHelper.updateDataAny({
          table: "loan_contract_document",
          columns: ["is_deleted", "updated_date"],
          values: [1, new Date()],
          conditions: {
            kunn_contract_number: kunn.kunn_id,
            is_deleted: 0,
            doc_type: listDocType,
            creation_time: {
              type: "date",
              toDate: yesterday,
            },
          },
        });
        await Promise.all(listDocType.map((docType) => genBizziKunnFile(kunn.kunn_id, docType)));
      } catch (err) {
        common.log(`[generateBizziKunnTemplateJobNextDay] Error generating template for KUNN ${kunn.kunn_id || kunn.id}: ${err.message}`);
      }
    }
    return true;
  } catch (err) {
    common.log(`[generateBizziKunnTemplateJobNextDay] Job error: ${err.message}`);
    return false;
  }
}

async function generateFinVDoc(contractNumber) {
  try {
    const listDocType = [DOC_TYPE.FVR, DOC_TYPE.LCT, DOC_TYPE.LD];
    for (const docType of listDocType) {
      await genFinVFile(contractNumber, docType);
    }
  } catch (error) {
    common.log(`[generateFinVDoc] error: ${error.message}`, contractNumber);
    console.error(error);
  }
  return false;
}

async function genFinVFile(contractNumber, docType){
  try {
    let data;
    switch (docType) {
      case "FVR":
        data = await getDataFinVForTemplateFVR(contractNumber);
        break;
      case "LCT":
        data = await getDataFinVForTemplateLCT(contractNumber);
        console.log(JSON.stringify(data));
        break;
      case "LD":
        data = await getDataFinVForTemplateLD(contractNumber);
        console.log(JSON.stringify(data));
        break;
      default:
        throw new Error(`Unsupported docType: ${docType}`);
    }
    const rs = await addTaskConvertFile(data);
    await saveStepLog(contractNumber, SERVICE_NAME.CONVERT_SERVICE, docType, data, rs);
  } catch (err) {
    common.log(`genFinVFile error: ${err.message}`, contractNumber);
    console.error(err);
  }
  return true;
}

const getDataFinVForTemplateFVR = async (contractNumber) => {
  try {
    const [loanContractData, loanCustomerData] = await Promise.all([
      loanContractRepo.findByContractNumber({ contractNumber, partnerCode: PARTNER_CODE.FINV }),
      loanCustomerRepo.getLoanCustomer(contractNumber),
    ]);
    const [wardCur, provinceCur, wardPer, provincePer, workplaceWard, workplaceProvince] = await Promise.all([
      getValueCode_v3(loanContractData.cur_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD),
      getValueCode_v3(loanContractData.cur_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      getValueCode_v3(loanContractData.per_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD),
      getValueCode_v3(loanContractData.per_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      getValueCode_v3(loanContractData.workplace_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD),
      getValueCode_v3(loanContractData.workplace_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE),
    ]);
    const businessData = JSON.parse(loanContractData.business_data);
    const perAddress = [loanContractData.address_per, wardPer, provincePer].filter(el => el).join(', ');
    const currentAddress = [loanContractData.address_cur, wardCur, provinceCur].filter(el => el).join(', ');
    const workplaceAddress = [loanContractData.workplace_address, workplaceWard, workplaceProvince].filter(el => el).join(', ');
    return {
      task_name: "GEN_FINV_DOC_FILE",
      body: {
        contract_number: contractNumber,
        doc_type: DOC_TYPE.FVR,
        contract_data: {
          sme_name: loanContractData.sme_name,
          registration_number: loanContractData.registration_number,
          address_on_license: loanCustomerData.address_on_license,
          sector_industry: loanContractData.sector_industry,
          cust_full_name: loanContractData.cust_full_name,
          address_per: perAddress,
          address_cur: currentAddress,
          workplace_address: workplaceAddress,
          time_duration: businessData.timeDuration,
          turnover_amount: utils.convertNumber(loanContractData.turnover_amount),
          business_cost: utils.convertNumber(businessData.businessCost),
          profit: utils.convertNumber(loanContractData.turnover_amount - businessData.businessCost),
          avg_turnover: utils.convertNumber(businessData.avgTurnover)
        },
      }
    };
  } catch (err) {
    common.log(`getDataFinVForTemplateFVR error: ${err.message}`, contractNumber);
    console.error(err);
  }
};

const getDataFinVForTemplateLCT = async (contractNumber) => {
  try {
    const loan = await loanContractRepo.findByContractNumber({ contractNumber, partnerCode: PARTNER_CODE.FINV });
    const [wardCur, provinceCur, wardPer, provincePer] = await Promise.all([
      getValueCode_v3(loan.cur_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD), 
      getValueCode_v3(loan.cur_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE), 
      getValueCode_v3(loan.per_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD), 
      getValueCode_v3(loan.per_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE),
    ]);
    const perAddress = [loan.address_per, wardPer, provincePer].filter(el => el).join(', ');
    const currentAddress = [loan.address_cur, wardCur, provinceCur].filter(el => el).join(', ');
    return {
      task_name: "GEN_FINV_DOC_FILE",
      body: {
        contract_number: contractNumber,
        doc_type: DOC_TYPE.LCT,
        contract_data: {
          contract_number: contractNumber,
          decision_no: '',
          ngay: dateHelper.getDate(),
          thang: dateHelper.getMonth(),
          nam: dateHelper.getYear(),
          customer_name: loan.cust_full_name,
          identity_card: loan.id_number,
          issue_date: formatDate(loan.id_issue_dt, DATE_FORMAT.DDMMYYYY),
          issue_place: loan.id_issue_place,
          per_address: perAddress,
          current_address: currentAddress,
          phone_number: loan.phone_number1,
          registration_number: loan.registration_number,
          registration_date: '',
          license_address: loan.address_on_license,
          total_turnover_next_year: utils.convertNumber(loan.total_turnover_next_year || 0),
          total_cost_next_year: utils.convertNumber(loan.total_cost_next_year || 0),
          pre_tax_profit_next_year: utils.convertNumber(loan.pre_tax_profit_next_year || 0),
          from_year: dateHelper.getYear() % 100,
          to_year: dateHelper.getYear() % 100 + 1,
          capital_need: utils.convertNumber(loan.capital_need || 0),
          owner_equity: utils.convertNumber(loan.owner_equity || 0),
          other_capital: utils.convertNumber(loan.other_capital || 0),
          evf_equity: utils.convertNumber(0),
          evf_equity_text: getText(0),
          approved_amt: utils.convertNumber(loan.approval_amt || 0),
          approved_amt_text: getText(Number(loan.approval_amt)),
          customer_name_uppercase: loan.cust_full_name.toUpperCase(),
          email: loan.email,
          gender: loan.gender,
          date_of_birth: formatDate(loan.birth_date, DATE_FORMAT.DDMMYYYY),
          permanent_new_province: provincePer || '',
          permanent_new_ward: wardPer || '',
          permanent_address: loan.address_cur,
          loan_amount_not_insurance: '',
          compensation_amount_kv: '',
          loan_insurance_kv: '',
          signed_date: dateHelper.VN_FORMAT_DATE(),
          tenor: loan.approval_tenor,
        },
      }
    };
  } catch (err) {
    common.log(`getDataFinVForTemplateLCT error: ${err.message}`, contractNumber);
    console.error(err);
  }
};

const getDataFinVForTemplateLD = async (contractNumber) => {
  try {
    const loan = await loanContractRepo.findByContractNumber({ contractNumber, partnerCode: PARTNER_CODE.FINV });
    const [wardCur, provinceCur, wardPer, provincePer, loanPurpose] = await Promise.all([
      getValueCode_v3(loan.cur_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD), 
      getValueCode_v3(loan.cur_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE), 
      getValueCode_v3(loan.per_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD), 
      getValueCode_v3(loan.per_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE), 
      getValueCode_v3(loan.workplace_new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD), 
      getValueCode_v3(loan.workplace_new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE), 
      getValueCode_v3(loan.loan_purpose, MASTER_DATA.CODE_TYPE.LOAN_PURPOSE), 
    ]);
    const currentAddress = [loan.address_cur, wardCur, provinceCur].filter(el => el).join(', ');
    const perAddress = [loan.address_per, wardPer, provincePer].filter(el => el).join(', ');
    const newPerAddress = [loan.address_cur, wardPer, provincePer].filter(el => el).join(', ');
    return {
      task_name: "GEN_FINV_DOC_FILE",
      body: {
        contract_number: contractNumber,
        doc_type: DOC_TYPE.LD,
        contract_data: {
          decision_no: '',
          ngay: dateHelper.getDate(),
          thang: dateHelper.getMonth(),
          nam: dateHelper.getYear(),
          customer_name: loan.cust_full_name,
          date_of_birth: formatDate(loan.birth_date, DATE_FORMAT.DDMMYYYY),
          identity_card: loan.id_number,
          issue_date: formatDate(loan.id_issue_dt, DATE_FORMAT.DDMMYYYY),
          issue_place: loan.id_issue_place,
          current_address: currentAddress,
          per_address: perAddress,
          new_per_address: newPerAddress,
          phone_number: loan.phone_number1,
          email: loan.email,
          registration_number: loan.registration_number,
          registration_date: '',
          license_address: loan.address_on_license,
          loan_amount: utils.convertNumber(loan.approval_amt || 0),
          loan_amount_text: getText(Number(loan.approval_amt)),
          tenor: loan.approval_tenor,
          loan_purpose: loanPurpose,
          date_signature: dateHelper.VN_FORMAT_DATE(),
        },
      }
    };
  } catch (err) {
    common.log(`getDataFinVForTemplateLD error: ${err.message}`, contractNumber);
    console.error(err);
  }
};

async function genFinVKunnFile(kunnId, docType) {
  try {
    let data = {};
    // const documentNos = generateKunnDocumentNo();
    switch (docType) {
      case "LCTKU":
        data = await getDataForTemplateLCTKU(kunnId);
        break;
      case "TTRV" :
        data = await getDataFinVForTemplateTTRV(kunnId);
        console.log(JSON.stringify(data));
        break;
      default:
        throw new Error(`Unsupported docType: ${docType}`);
    }
    const rs = await addTaskConvertFile(data);
    await saveStepLog(kunnId, SERVICE_NAME.KUNN_GENERATE_TEMPLATE, docType, data, rs);
    console.log(`Generated file for KUNN ID ${kunnId} docType ${docType}: ${JSON.stringify(rs)}`);
  } catch (err) {
    common.log(`genFinVKunnFile error: ${err.message}`, kunnId);
    console.error(err);
  }
  return true;
}

module.exports = {
  genBizziAF3File,
  generateContract,
  getContractDataHM,
  getContractDataHmSME,
  genBizziKunnFile,
  generateKunnContractFile,
  generateAF3ContractFile,
  checkGenerateKunnTemplateSuccess,
  checkGenerateKunnTemplateSuccessV2,
  cancelContract,
  generateBizziCreditLimitTemplateJobNextDay,
  generateBizziKunnTemplateJobNextDay,
};
