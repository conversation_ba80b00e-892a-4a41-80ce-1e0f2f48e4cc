const express = require("express");
const { handleResponseError } = require("../base/response.js");
const authService = require("../utils/aaaService.js");
const finvService = require("../services/finv-service.js");
const finvValidator = require("../utils/validator/finv-validator.js");
const { verifySignedRequestFromPartner, injectPartnerHeaders } = require("../utils/middleware.js");
const { PARTNER_CODE } = require("../const/definition.js");
const { handleResponse } = require("../entity/response-base-finv");
const { FinvA1 } = require("../A1_BASE/a1-app-form-service.js");
const { finvA2 } = require("../A2_BASE/a2-app-form-service.js");
const { masterDataFinvSchema, presignUploadDocFinvSchema } = require("../KUNN/schema/finv-scheme.js");

const router = express.Router();

router.post(
  "/v1/finv/document/presigned-s3",
  (req, res, next) => finvValidator.validate(req, res, presignUploadDocFinvSchema, next),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    try {
     const result = await finvService.getPresignedUploadDocumentUrl(req, res);
     return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/v1/finv/masterdata",
  injectPartnerHeaders(PARTNER_CODE.FINV),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  (req, res, next) => finvValidator.validate(req, res, masterDataFinvSchema, next),
  async (req, res) => {
    try {
      const result = await finvService.getMasterdata(req, res);
      return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/v1/finv/check-consent",
  injectPartnerHeaders(PARTNER_CODE.FINV),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    try {
     const result = await finvService.checkConsent(req, res);
     return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/v1/finv/credit-limit/:contractNumber/available-limit",
  injectPartnerHeaders(PARTNER_CODE.FINV),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    try {
      const result = await finvService.checkAvailableLimit(req.params.contractNumber);
      return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/v1/finv/check-a1",
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  finvValidator.FinvAf1Validate,
  (req, res) => {
    const finvA1 = new FinvA1(req, res);
    finvA1.createLoan();
  }
);

router.post(
  "/v1/finv/check-a2",
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  finvValidator.FinvFullloanValidate,
  (req, res) => {
    const finv2 = new finvA2(req, res);
    finv2.a2Receive();
  }
);

router.post("/v1/loan-request/change-request/approve", async (req, res) => {
  try {
    const result = await finvService.approveChangeRequest(req, res);
    return handleResponse(res, result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/loan-request/change-request/reject", async (req, res) => {
  try {
    const result = await finvService.rejectChangeRequest(req, res);
    return handleResponse(res, result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/loan-request/sub-info/process", async (req, res) => {
  try {
    const { contract_number, sub_type, reference_id, info, request_id } = req.body;
    const result = await finvService.processSubInfo({ contract_number, sub_type, reference_id, info, request_id });
    return handleResponse(res, result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/loan-request/process", async (req, res) => {
  try {
    const result = await finvService.processData(req, res);
    return handleResponse(res, result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post(
  "/v1/finv/resubmit-a2",
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    const result = await finvService.a2ReSubmit(req, res);
    return handleResponse(res, result);
  }
);

router.post(
  "/v1/finv/check-a3",
  authService.authenticateOauth2V04WithToken,
  verifySignedRequestFromPartner,
  finvValidator.finvA3Validate,
  (req, res) => {
    const finv2 = new finvA2(req, res);
    finv2.a3Receive();
  }
);

router.post("/v1/finv/credit-limit/:contract_number/status",
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  finvValidator.validateGetContractStatus,
  async (req, res) => {
    const result = await finvService.getContractStatus(req, res);
    return handleResponse(res, result);
  }
);

router.post("/v1/finv/credit-limit/:contract_number/documents",
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  finvValidator.validateGetPresignedFiles,
  injectPartnerHeaders(PARTNER_CODE.FINV),
  async (req, res) => {
    const result = await finvService.getPresignedDownloadDocumentUrl(req, res);
    return handleResponse(res, result);
});

router.post(
  "/v1/finv/kunn",
  injectPartnerHeaders(PARTNER_CODE.FINV),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    try {
      const result = await finvService.submitKunn(req, res);
      return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/v1/finv/gen-file-result",
  async (req, res) => {
    try {
      const result = await finvService.genFileResult(req,res)
      return res.json(result)
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post("/v1/finv/af3/resubmit", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken, 
  finvValidator.finvA3ResubmitValidate,
  async (req, res) => {
  try {
    const result = await finvService.af3Resubmit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/finv/kunns/:debtContractNumber/installments", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken, 
  finvValidator.finvGetInstallmentValidate,
  async (req, res) => {
  try {
    const result = await finvService.getInstallmentByKunn(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/finv/kunns/:debtContractNumber/installments/tracking", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken, 
  finvValidator.finvGetInstallmentValidate,
  async (req, res) => {
  try {
    const result = await finvService.trackingInstallment(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/finv/fet-withdrawal", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken, 
  finvValidator.finvFetWithdrawalValidate,
  async (req, res) => {
  try {
    const result = await finvService.handleFetWithdrawal(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/finv/credit-limits/signing-inprogress/cancel", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken, 
  finvValidator.cancelCreditLimitSigningInProgress,
  async (req, res) => {
    try {
    const result = await finvService.cancelCreditLimitSigningInProgress(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/finv/credit-limit/:contractNumber/kunns/:debtContractNumber/status", 
  verifySignedRequestFromPartner, 
  authService.authenticateOauth2V04WithToken,
  finvValidator.finvGetKunnStatusValidate,
  async (req, res) => {
    try {
    const result = await finvService.getKunnContractStatus(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});



module.exports = router;
